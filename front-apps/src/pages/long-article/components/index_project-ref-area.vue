<template>
  <div class="ref-area">
    <div class="ref-header">
      <div class="header-title">
        <el-icon class="title-icon">
          <FolderOpened/>
        </el-icon>
        <h3 class="title-text">参考资料</h3>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="showUploadDialog" class="upload-btn">
          <el-icon>
            <Plus/>
          </el-icon>
          添加
        </el-button>
      </div>
    </div>

    <div class="ref-content">
      <div v-if="items.length === 0" class="empty-state">
        <el-icon class="empty-icon">
          <Document/>
        </el-icon>
        <p class="empty-text">暂无参考资料</p>
        <p class="empty-hint">点击上方"添加"按钮上传文档</p>
      </div>

      <div v-else class="ref-list">
        <div
            v-for="(item, index) in items"
            :key="index"
            class="ref-item"
            :class="{ 'understood': item.state === 1, 'processing': item.state === 2 }"
        >
          <div class="item-icon">
            <el-icon v-if="item.state === 0" class="icon waiting">
              <Clock/>
            </el-icon>
            <el-icon v-else-if="item.state === 1" class="icon success">
              <CircleCheck/>
            </el-icon>
            <el-icon v-else-if="item.state === 2" class="icon processing">
              <Loading/>
            </el-icon>
          </div>

          <div class="item-content">
            <div class="item-name" :title="item.name">{{ item.name }}</div>
            <div class="item-meta">
              <div class="item-status">
                <span v-if="item.state === 0" class="status waiting">待理解</span>
                <span v-else-if="item.state === 1" class="status understood">已理解</span>
                <span v-else-if="item.state === 2" class="status processing">理解中...</span>
              </div>
<!--              <div class="item-type">-->
<!--                <span class="type-label" :class="getTypeClass(item.usType)">-->
<!--                  {{ getTypeName(item.usType) }}-->
<!--                </span>-->
<!--              </div>-->
            </div>
          </div>

          <div class="item-actions">
<!--            <el-button-->
<!--                v-if="item.state === 0"-->
<!--                type="primary"-->
<!--                size="small"-->
<!--                @click="processItem(index)"-->
<!--                class="process-btn"-->
<!--            >-->
<!--              理解-->
<!--            </el-button>-->
          </div>

          <!-- 删除按钮 - 右上角大叉 -->
          <div class="delete-btn" @click="removeItem(item, index)">
            <el-icon>
              <Close/>
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传文件对话框 -->
    <el-dialog
        title="添加参考资料"
        v-model="uploadDialogVisible"
        width="600px"
        :close-on-click-modal="false"
        :show-close="false"
        class="upload-dialog"
    >
      <div class="upload-content">
        <!-- 理解类型选择 -->
        <div class="understanding-type-section">
          <el-form :model="uploadForm" label-width="100px" class="upload-form">
            <el-form-item label="理解类型" required>
              <el-select
                  v-model="uploadForm.understandingType"
                  placeholder="请选择理解类型"
                  style="width: 100%"
              >
                <el-option
                    v-for="option in understandingTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled="option.disabled"
                >
                  <div class="option-content">
                    <span class="option-label">{{ option.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 文件上传区域 -->
        <div class="file-upload-section">
          <el-upload
              ref="uploadRef"
              class="upload-dragger"
              drag
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              :on-remove="handleFileRemove"
              :accept="getSupportFormatToAccept()"
              multiple
          >
            <el-icon class="el-icon--upload">
              <UploadFilled/>
            </el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持的格式：{{getSupportFormatToDisplay()}}
              </div>
            </template>
          </el-upload>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">返回</el-button>
          <el-button
              type="primary"
              @click="confirmUpload"
              :disabled="fileList.length === 0 || uploadForm.understandingType === null"
          >
            确认上传
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  FolderOpened,
  Plus,
  Document,
  Clock,
  CircleCheck,
  Loading,
  Delete,
  UploadFilled,
  Close
} from '@element-plus/icons-vue'
import {sleep} from "../../../code/util/thread-util.js";

/**
 * 参考资料区
 */
export default {
  name: 'index_project-ref-area',
  components: {
    FolderOpened,
    Plus,
    Document,
    Clock,
    CircleCheck,
    Loading,
    Delete,
    UploadFilled,
    Close
  },
  props: {
    projectForm: null,
  },
  data() {
    return {
      // supportFileFormats: ['docx', 'txt', 'md'],
      understandingTypeOptions: [
        {label: '纯文本理解', value: 0, disabled: false, supportFileFormats: ['docx', 'txt', 'md']},
        {label: '纯图片理解', value: 1, disabled: false, supportFileFormats: ['png', 'jpg', 'jpeg']},
        // {label: '图文混合理解', value: 2, disabled: true},
      ],
      // 参考资料列表
      items: [
        // state 状态：0 待理解，1 已理解，2 理解中
        // usType 理解类型：0 纯文本理解，1 纯图片理解，2 图文混合理解
        // {name: '项目需求文档.docx', state: 0, usType: 0},
        // {name: '设计图片集合.pdf', state: 1, usType: 1},
        // {name: '技术方案报告.doc', state: 1, usType: 2},
        // {name: '用户调研数据.xlsx', state: 0, usType: 0},
        // {name: '用户调研数据.xlsx', state: 0, usType: 0},
        // {name: '用户调研数据.xlsx', state: 0, usType: 0},
        // {name: '用户调研数据.xlsx', state: 0, usType: 0},
      ],
      // 上传对话框
      uploadDialogVisible: false,
      fileList: [],
      // 上传表单
      uploadForm: {
        understandingType: 0
      }
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    reset() {
      this.fileList = []
      this.uploadForm.understandingType = 0
    },
    async onShowed() {
      await this.loadData();
    },
    /**
     * 显示上传对话框
     */
    showUploadDialog() {
      this.reset()
      this.uploadDialogVisible = true
    },

    async loadData() {
      const list = await this.getCtx().longArticleReferenceMapper.loadReferenceList(this.projectForm.id)
      this.items = list;
    },

    getSupportFormats() {
      for (const option of this.understandingTypeOptions) {
        if (option.value === this.uploadForm.understandingType) {
          return option.supportFileFormats;
        }
      }
      return [];
    },

    getSupportFormatToAccept() {
      let str = '';
      let index = 0;
      for (const fmt of this.getSupportFormats()) {
        if (index > 0) {
          str += ',';
        }
        str += `.${fmt}`;
        index++;
      }
      return str;
    },

    getSupportFormatToDisplay() {
      let str = '';
      let index = 0;
      for (const fmt of this.getSupportFormats()) {
        if (index > 0) {
          str += ' / ';
        }
        str += `.${fmt}`;
        index++;
      }
      return str;
    },

    /**
     * 获取理解类型名称
     */
    getTypeName(value) {
      const names = {
        0: '纯文本',
        1: '纯图片',
        2: '图文混合'
      }
      return names[value] || '未知'
    },

    /**
     * 获取理解类型样式类
     */
    getTypeClass(value) {
      const classes = {
        0: 'type-text',
        1: 'type-image',
        2: 'type-mixed'
      }
      return classes[value] || 'type-unknown'
    },

    /**
     * 上传文件变化
     */
    handleFileChange(file, fileList) {
      this.fileList = fileList
      console.log('上传文件变化', file, fileList)

      if (this.getSupportFormats().find((n) => {
        return file.name.endsWith(`.${n}`)
      }) == null) {
        this.$alert(`暂不支持文件“${file.name}”的格式`, { type: 'error' });
        for (const i in fileList) {
          const file0 = fileList[i];
          if (file.name === file0.name) {
            fileList.splice(i, 1);
            break;
          }
        }
      }
    },

    /**
     * 删除上传文件
     * @param file
     * @param fileList
     */
    handleFileRemove(file, fileList) {
      this.fileList = fileList
      console.log('删除上传文件', file, fileList)
    },

    /**
     * 确认上传
     */
    async confirmUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请选择要上传的文件')
        return
      }

      if (this.uploadForm.understandingType === null) {
        this.$message.warning('请选择理解类型')
        return
      }

      const loading = this.$loading({ lock: true, text: '上传中' });
      try {
        // 添加文件到列表
        for (const elFile of this.fileList) {
          const file = elFile.raw;
          const fileItem = {
            name: file.name,
            size: file.size,
            progress: 0,
            file: file
          };

          const r = await this.getCtx().longArticleReferenceMapper.uploadDocToMd({
            fileItem,
            projectId: this.projectForm.id,
            understandingType: this.uploadForm.understandingType,
          }, (pars) => {
            if (pars.progress >= 0) {
              loading.text = `上传中（${pars.progress}%）`;
            }
          }, (fileItem, message) => {
          })

          if (r.success === false) {
            this.$alert(`上传失败：${r.msg}`, { type: 'error' });
            // this.$message.error('上传失败1：' + r.msg);
          }
          else {
            await sleep(1000 * 1);

            this.$message.success(`成功上传 ${this.fileList.length} 个文件`)
            this.uploadDialogVisible = false
            await this.loadData();
          }
        }

      } catch (error) {
        this.$alert(`上传失败：${error.message}`, { type: 'error' });
        // this.$message.error('上传失败2：' + error.message)
      } finally {
        loading.close();
      }
    },

    /**
     * 移除文档
     */
    removeItem(item, index) {
      const self = this;

      this.$confirm('确定要删除这个参考资料吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        self.items.splice(index, 1)
        self.getCtx().longArticleReferenceMapper.deleteReference(self.projectForm.id, item.name)
        self.$message.success('删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 获取已理解的文档列表
     */
    getUnderstoodItems() {
      return this.items.filter(item => item.state === 1).map(item => ({
        ...item,
        typeName: this.getTypeName(item.usType),
        typeDescription: this.getTypeDescription(item.usType)
      }))
    },

    /**
     * 清空所有文档
     */
    clearAll() {
      this.items = []
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.ref-area {
  height: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  .ref-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .header-title {
      display: flex;
      align-items: center;

      .title-icon {
        font-size: 24px;
        margin-right: 12px;
        color: rgba(255, 255, 255, 0.9);
      }

      .title-text {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        color: rgba(255, 255, 255, 0.95);
      }
    }

    .header-actions {
      .upload-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.4);
          transform: translateY(-1px);
        }

        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }

  .ref-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    max-height: 400px;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 150px;
      color: #9ca3af;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
      }

      .empty-text {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 8px 0;
        color: #6b7280;
      }

      .empty-hint {
        font-size: 14px;
        margin: 0;
        opacity: 0.8;
      }
    }

    .ref-list {
      .ref-item {
        display: flex;
        align-items: center;
        padding: 14px;
        margin-bottom: 10px;
        background: white;
        border-radius: 10px;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        //overflow: hidden;
        gap: 12px;

        &:hover {
          .delete-btn {
            opacity: 1;
            visibility: visible;
          }
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: #d1d5db;
          transition: all 0.3s ease;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          border-color: #c7d2fe;
        }

        &.understood {
          border-color: #86efac;
          background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);

          &::before {
            background: #22c55e;
          }
        }

        &.processing {
          border-color: #fbbf24;
          background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);

          &::before {
            background: #f59e0b;
          }

          .icon.processing {
            animation: spin 1s linear infinite;
          }
        }

        .item-icon {
          flex-shrink: 0;
          display: flex;
          align-items: center;

          .icon {
            font-size: 18px;

            &.waiting {
              color: #6b7280;
            }

            &.success {
              color: #22c55e;
            }

            &.processing {
              color: #f59e0b;
            }
          }
        }

        .item-content {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          gap: 6px;

          .item-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
          }

          .item-meta {
            display: flex;
            align-items: center;
            gap: 8px;

            .item-status {
              .status {
                font-size: 11px;
                padding: 3px 8px;
                border-radius: 10px;
                font-weight: 500;
                display: inline-block;

                &.waiting {
                  background: #f3f4f6;
                  color: #6b7280;
                }

                &.understood {
                  background: #dcfce7;
                  color: #166534;
                }

                &.processing {
                  background: #fef3c7;
                  color: #92400e;
                }
              }
            }

            .item-type {
              .type-label {
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 6px;
                font-weight: 500;
                white-space: nowrap;
                display: inline-block;

                &.type-text {
                  background: #e0f2fe;
                  color: #0277bd;
                  border: 1px solid #b3e5fc;
                }

                &.type-image {
                  background: #f3e5f5;
                  color: #7b1fa2;
                  border: 1px solid #e1bee7;
                }

                &.type-mixed {
                  background: #fff3e0;
                  color: #ef6c00;
                  border: 1px solid #ffcc02;
                }

                &.type-unknown {
                  background: #f5f5f5;
                  color: #757575;
                  border: 1px solid #e0e0e0;
                }
              }
            }
          }
        }

        .item-actions {
          flex-shrink: 0;
          display: flex;
          align-items: center;

          .process-btn {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
          }
        }

        .delete-btn {
          position: absolute;
          top: -8px;
          right: -8px;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #ef4444;
          border: 1px solid #dc2626;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          //opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);

          &:hover {
            background: #dc2626;
            border-color: #b91c1c;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
          }

          .el-icon {
            font-size: 14px;
            color: white;
          }
        }
      }
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

/* 上传对话框样式 */
:deep(.upload-dialog) {
  margin-top: 30px;
  margin-bottom: 30px;

  .el-dialog {
    border-radius: 16px;
    overflow: hidden;

  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
  }

  .el-dialog__body {
    padding: 12px;

    .upload-content {
      .understanding-type-section {
        margin-bottom: 12px;
        padding: 10px;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e2e8f0;

        .upload-form {
          margin: 0;

          :deep(.el-form-item) {
            margin-bottom: 0;

            .el-form-item__label {
              font-weight: 600;
              color: #374151;
            }

            .el-select {
              .el-input__wrapper {
                border-radius: 8px;
              }
            }
          }
        }

        .option-content {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .option-label {
            font-weight: 500;
            color: #374151;
          }

          .option-desc {
            font-size: 12px;
            color: #6b7280;
          }
        }
      }

      .file-upload-section {
        .upload-dragger {
          .el-upload-dragger {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            background: #fafafa;
            transition: all 0.3s ease;
            padding: 20px 10px;

            &:hover {
              border-color: #667eea;
              background: #f8fafc;
            }

            .el-icon--upload {
              font-size: 48px;
              color: #9ca3af;
              margin-bottom: 16px;
            }

            .el-upload__text {
              color: #6b7280;
              font-size: 14px;

              em {
                color: #667eea;
                font-style: normal;
                font-weight: 500;
              }
            }
          }

          :deep(.el-upload__tip) {
            color: #9ca3af;
            font-size: 12px;
            margin-top: 8px;
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    background: #fafafa;
    border-top: 1px solid #e5e7eb;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

      .el-button {
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 500;

        &.el-button--primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;

          &:hover:not([disabled]) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }
          
          &[disabled] {
            background: #a5a5a5;
            opacity: 0.7;
            cursor: not-allowed;
            box-shadow: none;
            
            &:hover {
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }
}
</style>