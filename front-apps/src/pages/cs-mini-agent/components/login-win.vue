<template>
    <div class="login-win" v-if="visible">
        <div class="mask"></div>
        <div class="win-content">
            <div class="win-header">
                <span class="title">请登录</span>
            </div>
            <div class="win-body">
                <Login ref="login0" @login="handleLogin" />
            </div>
        </div>
    </div>
</template>

<script>
import Login from './login.vue'

export default {
    name: 'LoginWin',
    components: {
        Login
    },
    props: {
    },
    data() {
        return {
            visible: false,
        }
    },
    methods: {
        async show(cfg) {
            const self = this;
            this.visible = true;

            self.loginForm = cfg.loginForm;
            self._handleLogin = cfg.handleLogin;

            self.$nextTick(() => {
                self.$refs.login0.setForm(self.loginForm);
            })
        },
        async handleLogin(e) {
            const self = this;
            await this._handleLogin({
                sac: e.sac,
                username: e.username,
                password: e.password,
                jzmm: e.jzmm,
                doneCallback: (r) => {
                    if (r.success) {
                        self.visible = false;
                    }
                    e.doneCallback(r);
                }
            });
        }
    }
}
</script>

<style scoped>
.login-win {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.win-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 90%; /* 为了兼容手机版 */
    max-width: 400px;
}

.win-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.win-body {
    padding: 20px;
}
</style>