import appConfig from "../../../../config/app-config.js";
import CBAIOMapper from "../../../mapper/CBAIOMapper.js"

export class DocImportMapper {

    constructor(ctx) {
        this.ctx = ctx;
        this.cbaiomapper = new CBAIOMapper(ctx);
    }

    /**
     *
     * @param pars fileItem, kbId
     * @param onProgressChange
     * @param onError
     * @return {Promise<void>}
     */
    async uploadDocToMd(pars, onProgressChange, onError) {
        const token = await this.ctx.loginService.getToken();
        const { fileItem, kbId } = pars;
        const formData = new FormData()


        // 创建一个新的File对象，确保文件名正确编码
        const encodedFile = new File([fileItem.file], fileItem.file.name, {
            type: fileItem.file.type,
            lastModified: fileItem.file.lastModified
        })

        formData.append('token', token)
        formData.append('kbId', kbId)
        formData.append('file', encodedFile)
        // 额外添加原始文件名，确保服务端能正确识别
        formData.append('originalName', encodeURIComponent(fileItem.file.name))

        try {
            const xhr = new XMLHttpRequest()

            // 监听上传进度
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    fileItem.progress = Math.round((e.loaded / e.total) * 100)
                }
            })

            // 监听请求完成
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText)
                        console.log('文件上传成功:', fileItem.name, response)

                        if (onProgressChange) {
                            onProgressChange({
                                fileItem, response,
                            });
                        }
                    } catch (error) {
                        console.error('解析响应失败:', error)
                        // this.handleUploadError(fileItem, '响应解析失败')
                        if (onError) onError(fileItem, '响应解析失败');
                    }
                } else {
                    console.error(`上传失败 -> ${xhr.status}`, xhr.status, xhr.statusText, xhr.response)
                    if (onError) onError(fileItem, xhr.response);
                }
            })

            // 监听请求错误
            xhr.addEventListener('error', () => {
                console.error('网络错误')
                if (onError) onError(fileItem, '网络错误')
            })

            // 发送请求
            xhr.open('POST', `${appConfig.backRootUrl}api/doc-import/doc-to-md`)
            // 设置请求头确保中文文件名正确传输
            // xhr.setRequestHeader('Accept-Charset', 'UTF-8')
            xhr.send(formData)

        } catch (error) {
            console.error('上传异常:', error)
            if (onError) onError(fileItem, '上传异常')
        }
    }

    /**
     * 对文档进行切片
     * @param content
     * @returns {Promise<*>}
     */
    async splitDoc(content) {
        const r = await this.cbaiomapper.reqByPost('/api/doc-import/split-doc', { content });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }
}