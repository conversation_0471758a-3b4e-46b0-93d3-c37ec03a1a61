<template>
  <div ref="csMiniAgent0" class="cs-mini-agent" :style="{ maxWidth: getMaxInnerWidthStr() }">
    <div class="main-content">

      <!-- 左栏：会话列表 -->
      <div class="session-list" v-show="hideLeftBar !== true">
        <div class="list-header">会话列表</div>
        <div class="list-content">
          <my-chat-list-bar ref="myChatListBar0" @on-select-session="handleSelectSession"></my-chat-list-bar>
        </div>
        <div class="new-button-area">
          <button class="add-agent-btn" @click="handleAddAgent">
            <i class="el-icon-plus"></i>
            <span>新增专员</span>
          </button>
        </div>
        <div class="bottom-bar" v-if="ui.showLeftBottomBar">
          <div>{{ loginUserRealname }}</div>
          <div style="width:15px;height: 100%;"></div>
          <div class="logout-btn" @click="handleLogout">
            <img :src="logoutIcon" alt="登出" title="登出" />
          </div>
        </div>
      </div>

      <!-- 中栏：聊天区 -->
      <div class="chat-area">
        <div class="chat-header">
          {{ currentSession?.title || '请选择会话' }}
          <!-- 适应模式 -->
          <div class="head-right-button adapt-mode-btn" :title="selfAdaptionType === 0 ? '切换到全屏模式' : '切换到普通模式'"
               v-if="canToggleSelfAdaptionType" style="float:right;"
            @click="toggleAdaptMode">
            <!-- <i :class="['el-icon', selfAdaptionType === 0 ? 'FullScreen' : 'el-icon-copy-document']"></i> -->
            <el-icon class="el-icon" v-if="selfAdaptionType === 0">
              <FullScreen />
            </el-icon>
            <el-icon class="el-icon" v-else>
              <CopyDocument />
            </el-icon>
            <!-- <i :class="['el-icon', 'el-icon-copy-document']" v-else></i> -->
          </div>
          <!-- 新建对话 -->
          <img class="head-right-button new-chat-btn" title="新建对话" @click="handleNewChat"
            src="./assets/img/new-chat.svg" />
          <!-- 登出 -->
          <div class="head-right-button new-chat-btn" title="登出" @click="handleLogout" v-if="canShowTopLogout">
            <img class="head-right-button new-chat-btn" :src="logoutIcon" alt="登出" title="登出" />
          </div>
          <!-- 编辑SCA会话 -->
          <div class="head-right-button" style="float:right;" title="编辑专员" @click="handleEditAgent($refs.myChatListBar0.getSelectedSessionId())">
            <img v-if="isSACSessionSelected" src="./assets/img/edit.svg" style="height: 20px;" />
          </div>
          <!-- 删除SCA会话 -->
          <div class="head-right-button" style="float:right;" title="删除专员" @click="handleDeleteAgent($refs.myChatListBar0.getSelectedSessionId())">
            <img v-if="isSACSessionSelected" src="./assets/img/delete.svg" style="height: 20px;" />
          </div>
        </div>
        <div class="chat-messages" ref="chatHistoryArea0">
          <!-- 消息列表 -->
          <template v-if="chatHistory && chatHistory.length > 0">
            <div v-for="msg in chatHistory" :key="msg.id" :class="['message-item', { 'message-mine': msg.isMe }]">
              <div class="message-time">{{ formatTime(msg.time) }}</div>
              <div class="agent-thinking" v-if="msg.thinking">
                <img :src="loadingGif" />
                <span v-if="msg.thinkState && msg.thinkState.length">（{{ msg.thinkState }}）</span>
                <span v-else>（思考中）</span>
              </div>
              <div class="message-reasoning-content"
                v-if="msg.reasoningContent && msg.reasoningContent.trim().length > 0">
                <div class="reasoning-header" @click="handleReasoningToggle(msg)">
                  <span class="reasoning-title">
                    AI思考过程
                    <span v-if="msg.reasoningContent && msg.reasoningContent.trim().length > 0">（{{
                      msg.reasoningContent.length }}）</span>
                  </span>
                  <span class="toggle-icon" :class="{ 'expanded': msg.isReasoningExpanded }">▼</span>
                </div>
                <div class="reasoning-content" v-show="msg.isReasoningExpanded">
                  <pre>{{ msg.reasoningContent }}</pre>
                </div>
              </div>

              <!-- 专员消息 -->
              <template v-if="msg.role === 'agent'">
                <div ref="messageContent0" class="message-content" v-if="msg.content && msg.content.length > 0"
                  @contextmenu.prevent="showLeftMsgContextMenu($event, msg)" v-html="renderMarkdown(msg.content)">
                </div>
                <div class="open-interaction-panel-link" v-if="msg.interactionPanelSettings">
                  <a href="javascript:;" @click="openInteractionPanel(msg.interactionPanelSettings)">打开交互面板</a>
                </div>
                <div class="agent-saying" v-if="msg.saying">
                  <img :src="loadingGif" />
                  <span v-if="msg.thinkState && msg.thinkState.length">（{{ msg.thinkState }}）</span>
                </div>
              </template>

              <!-- 人类消息 -->
              <template v-else>
                <div class="message-content" v-if="msg.content && msg.content.length > 0"
                  @contextmenu.prevent="showRightMsgContextMenu($event, msg)">
                  <pre>{{ msg.content }}</pre>
                </div>
                <div class="message-content" v-else @contextmenu.prevent="showRightMsgContextMenu($event, msg)">&nbsp;
                </div>
                <div class="open-interaction-panel-link" v-if="msg.interactionPanelSettings">
                  <a href="javascript:;" @click="openInteractionPanel(msg.interactionPanelSettings)">打开交互面板</a>
                </div>
              </template>
            </div>
          </template>
          <!-- 初始提示词区 -->
          <div v-if="(!chatHistory || chatHistory.length === 0) && initialPromptSettings.visible && initialPromptSettings.content
            && initialPromptSettings.content.length > 0" class="initial-prompt">
            <div class="initial-prompt-content" v-html="renderMarkdown(initialPromptSettings.content)"></div>
          </div>
        </div>
        <!-- 用户输入区 -->
        <div class="chat-input" @click="handleChatInputAreaClick">
          <chat-input-files ref="chatInputFiles0"></chat-input-files>
          <textarea v-model="inputMessage" :disabled="!currentSession || sending" @keydown="handleKeydown"
            @input="adjustTextareaHeight" ref="messageInput" placeholder="按Enter发送，Shift + Enter换行"
            @paste="handleInputPaste"></textarea>
          <div>
            <div class="botton-left-button" style="" v-if="agentFrontSettings">
              <chat-input-inner-tools ref="chatInputInnerTools0" :tools="agentFrontSettings.inputInnerTools"
                :can-select-filterable="canInputSelectFilterable" v-model="agentValues"
                @on-change="handleInputInnerToolsChange"></chat-input-inner-tools>
            </div>
            <!-- 发送按钮 -->
            <div class="bottom-right-button" style="margin-left: 10px;">
              <button v-if="!this.sending" @click="handleChatInputSendButtonClick" :disabled="!canChat()">发送</button>
              <button v-else class="stop-button" @click="handleChatInputStopButtonClick" :disabled="!this.chatStreamStateId">停止</button>
            </div>
            <!-- 语音输入 -->
            <div class="bottom-right-button" title="语音输入" v-if="canVoiceInput" style="margin-left: 10px;display: flex;align-items: center;">
              <img src="./assets/img/microphone.svg" :class="{ 'disabled': !canChat() }"
                @click="canChat() && $refs.speechInputFloatPanel0.show()"
                :style="{ height: '25px', cursor: canChat() ? 'pointer' : 'not-allowed' }" />
            </div>
            <!-- 文件上传 -->
            <div class="bottom-right-button" title="文件上传" v-if="canChat() && canUploadFile"
              style="display: flex;align-items: center;flex-direction: row;">
              <el-upload class="avatar-uploader" style="height: 20px;" :show-file-list="false" :auto-upload="false"
                :on-change="handleUploadChange">
                <el-icon :size="20">
                  <Files />
                </el-icon>
              </el-upload>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右栏：交互面板 -->
    <div class="floating-tag" v-if="interactionPanelSettings.enabled"
      :class="{ 'panel-visible': interactionPanelSettings.visible }"
      :style="`right:${(interactionPanelSettings.visible ? interactionPanelSettings.width : 0)}px;`"
      @click="toggleInteractionPanel">
      <span class="tag-icon">⚙</span>
      <span class="tag-text">交<br>互<br>面<br>板</span>
    </div>

    <!-- 背景遮罩 -->
    <transition name="fade" v-if="interactionPanelSettings.enabled">
      <div class="panel-overlay" v-if="interactionPanelSettings.visible" @click="closeInteractionPanel"></div>
    </transition>

    <div class="interaction-panel" v-if="interactionPanelSettings.enabled"
      :class="{ 'panel-visible': interactionPanelSettings.visible }"
      :style="`right:${(interactionPanelSettings.visible ? 0 : -interactionPanelSettings.width)}px;width:${interactionPanelSettings.width}px;`">
      <div class="panel-header">交互面板</div>
      <div class="panel-content">
        <!-- 这里可以根据需要添加交互组件 -->
        <interaction-panel ref="interactionPanel0" @on-submit="handlerInteractionPanelSubmit"
          @on-back="closeInteractionPanel"></interaction-panel>
      </div>
    </div>
    <login-win ref="loginWin0"></login-win>

    <!-- 右键菜单 -->
    <div class="context-menu" v-show="showLeftMsgMenu" :style="menuPosition">
      <div class="menu-item" @click="deleteMessage(true)">清空</div>
    </div>
    <div class="context-menu" v-show="showRightMsgMenu" :style="menuPosition">
      <div class="menu-item" @click="resendMessage">重发</div>
      <div class="menu-item" @click="editMessage">编辑</div>
      <div class="menu-item" @click="deleteMessage(false)">删除</div>
      <div class="menu-item" @click="deleteMessage(true)">清空</div>
    </div>
    <speech-input-float-panel ref="speechInputFloatPanel0"
      @on-send="inputMessage = $event; handleChatInputSendButtonClick()" @on-edit="inputMessage = $event;"
      @on-add="handleSpeechInputAdd($event)"></speech-input-float-panel>

    <!-- 自定义专员表单弹框 -->
    <uca-form-win ref="ucaFormWin0"></uca-form-win>
  </div>
</template>

<script>
/**
 * *** url参数说明 ***
 * sat 自适应类型
 * mobileMode: '1' 移动端模式
 * hideLeftBar: '1' 隐藏左栏
 * selectSessionId 选中会话ID
 * 
 * 样例：/?mobileMode=1&selectSessionId=agent-jzzs#/cma
 */

import sysCfg from '../../config/app-config';
import indexMixin_api from './mixin/index-mixin_api';
import indexMixin_util from './mixin/index-mixin_util';
import indexMixin_userClientData from './mixin/index-mixin_user-client-data';
import indexMixin_interactionPanel from './mixin/index-mixin_interaction-panel';
import indexMixin_chatList from './mixin/index-mixin_chat-list';
import indexMixin_leftBar from './mixin/index-mixin_left-bar';
import { marked } from 'marked';

import LoginService from '../../code/module/platform/service/LoginService';
import ChatService from '../../code/module/cs-mini-agent/service/ChatService';
import LoginViewModel from '../../code/module/platform/view-model/LoginViewModel';

import MyChatListBar from './components/my-chat-list-bar.vue';
import LoginWin from './components/login-win.vue';
import ChatInputInnerTools from './components/chat-input-inner-tools.vue';
import ChatInputFiles from './components/chat-input-files.vue';
import InteractionPanel from './components/interaction-panel.vue';
import SpeechInputFloatPanel from './components/speech-input-float-panel.vue';
import UcaFormWin from './components/uca-form-win.vue';
import { queryString } from '../../code/util/web-util';

// 导入图片资源
import logoutIcon from "./assets/img/logout.svg";
import loadingGif from "./assets/img/loading.gif";
import AgentMapper from '../../code/module/cs-mini-agent/mapper/AgentMapper';
import ChatMapper from '../../code/module/cs-mini-agent/mapper/ChatMapper';
import { UserCreateAgentMapper } from '../../code/module/cs-mini-agent/mapper/UserCreateAgentMapper';
import { waitGetObj } from '../../code/util/code-util';
import indexMixin_uca from "./mixin/index-mixin_uca.js";
import {ClientDataService} from "../../code/module/platform/service/ClientDataService.js";
import indexMixin_handler from "./mixin/index-mixin_handler.js";

export default {
  name: 'CsMiniAgent',
  props: {
    openAppName: {
      type: String,
      default: null,
    },
    autoShow: null,
  },
  mixins: [indexMixin_api, indexMixin_util, indexMixin_userClientData, indexMixin_handler,
    indexMixin_interactionPanel, indexMixin_chatList, indexMixin_leftBar, indexMixin_uca],
  components: {
    MyChatListBar, LoginWin, ChatInputInnerTools, ChatInputFiles, InteractionPanel, SpeechInputFloatPanel, UcaFormWin
  },
  data() {
    return {
      debug: sysCfg.debug,
      loading: false,

      // 自适应类型
      // 0 - 剧中窗口：宽度固定，高度自适应但是会与上下边距保持一个合适的距离，看起来不会很贴边
      // 1 - 贴边展开：宽度和高度都自适应并且都贴边
      // 2 - 剧中窗口（嵌入cs-ai-client）
      // 3 - 贴边展开（嵌入cs-ai-client）
      selfAdaptionType: 0,
      openerName: null, // 打开者（上级框架，例：cs-ai-client）
      canToggleSelfAdaptionType: true,
      // 能否上传文件
      canUploadFile: true,
      // 能否语音输入
      canVoiceInput: false,

      // 登录信息
      loginUserRealname: '',

      inputMessage: '', // 输入框消息

      sending: false, // 是否发送中
      /**
       * @type {ChatHistory}
       */
      chatHistory: [], // 聊天历史

      // 右键菜单相关
      showLeftMsgMenu: false,
      showRightMsgMenu: false,
      menuPosition: {
        top: '0px',
        left: '0px'
      },
      selectedMessage: null,

      maxTextareaHeight: 150, // 输入框最大高度

      agentFrontSettings: null,
      agentFrontSettings_default: {
        sendMessageType: 'stream',
      },
      agentValues: {},

      /*** 初始提示词 ***/
      // 初始提示词配置
      initialPromptSettings: {
        // 是否可见初始提示词区域
        visible: true,
        // 提示词内容
        content: '',
      },
      initialPromptSettings_default: {
        visible: true,
        content: '',
      },

      // 交互面板
      interactionPanelSettings: {
        enabled: false,
        visible: false,
        width: 0,
      },
      interactionPanelSettings_default: {
        enabled: false,
      },
      interactionPanelSettingsLeftLeaveSpace: 50,

      ui: {
        maxInnerWidth: 1000,
        showLeftBottomBar: true,
      },

      // 图片资源
      logoutIcon,
      loadingGif,
    }
  },
  methods: {

    getCtx() {
      return this.$ctx
    },
    async onShowed(pars = {}) {
      console.log('cs-mini-agent.onShowed')
      const self = this;

      self.loading = true

      if (pars == null) pars = {};
      if (pars.selfAdaptionType) {
        self.selfAdaptionType = pars.selfAdaptionType;
      }
      if (pars.openerName) {
        self.openerName = pars.openerName;
      }
      if (pars.showLeftBottomBar != null) {
        self.ui.showLeftBottomBar = pars.showLeftBottomBar;
      }

      let ctx = this.getCtx();
      if (ctx.loginViewModel == null) ctx.loginViewModel = new LoginViewModel(ctx, { self });
      if (ctx.chatService == null) ctx.chatService = new ChatService(ctx);
      if (ctx.agentMapper == null) ctx.agentMapper = new AgentMapper(ctx);
      if (ctx.chatMapper == null) ctx.chatMapper = new ChatMapper(ctx);
      if (ctx.userCreateAgentMapper == null) ctx.userCreateAgentMapper = new UserCreateAgentMapper(ctx);
      if (ctx.clientDataService == null) ctx.clientDataService = new ClientDataService(ctx);

      if (pars.selfAdaptionType == null) {
        // 通过url参数设置自适应类型
        const t_sat = queryString('sat');
        if (t_sat) {
          this.selfAdaptionType = parseInt(t_sat);
          this.canToggleSelfAdaptionType = false;
        } else {
          // 从本地存储中获取自适应类型设置
          const savedAdaptionType = await this.getClientData_selfAdaptionType()
          if (savedAdaptionType !== null) {
            self.selfAdaptionType = parseInt(savedAdaptionType, 10);
          }
        }
      }

      if (queryString('hideLeftBar') === '1') {
        this.hideLeftBar = true;
      }
      // 移动端模式
      if (queryString('mobileMode') === '1') {
        this.selfAdaptionType = 1;
        this.hideLeftBar = true;
        this.canToggleSelfAdaptionType = false;
        // this.canInputSelectFilterable = false; // 会导致下拉框点击无效
        this.canShowTopLogout = true;
      }

      if (queryString('canUploadFile') === '0') {
        this.canUploadFile = false;
      }

      // 登录检查
      await ctx.loginViewModel.checkLogin({
        readyCb() {
          self.loading = false
        },
        cb(pars1) {
          self.loginUserRealname = pars1.loginUser.realname;
        }
      });

      self.loading = true

      await this.$refs.myChatListBar0.onShowed();

      this.loadUrlParams();

      self.loading = false

      await this.onShowedEndProcLeftBar();
    },
    async onActivated(pars = {}) {
      console.log('cs-mini-agent.onActivated', pars)
    },
    getMaxInnerWidthStr() {
      if (this.selfAdaptionType === 0) {
        return `${this.ui.maxInnerWidth}px`;
      }
      else {
        return null;
      }
    },
    // 加载url参数
    loadUrlParams() {
    },
    canChat() {
      return this.currentSession && !this.sending;
    },
    // 加载聊天历史
    async loadChatHistory() {
      const list = await this.getCtx().chatMapper.getChatHistory(this.getChatHistorySessionId());
      if (list) {
        this.chatHistory = list;
      }

      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 显示左键菜单
    showLeftMsgContextMenu(event, msg) {
      event.preventDefault();

      if (!msg.isMe) {
        this.showLeftMsgMenu = true;
        this.selectedMessage = msg;
        this.menuPosition = {
          top: `${event.clientY}px`,
          left: `${event.clientX}px`
        };

        // 点击其他地方关闭菜单
        const closeMenu = () => {
          this.showLeftMsgMenu = false;
          document.removeEventListener('click', closeMenu);
        };
        document.addEventListener('click', closeMenu);
      }
    },

    // 显示右键菜单
    showRightMsgContextMenu(event, msg) {
      event.preventDefault();

      if (msg.isMe) {
        this.showRightMsgMenu = true;
        this.selectedMessage = msg;
        this.menuPosition = {
          top: `${event.clientY}px`,
          left: `${event.clientX}px`
        };

        // 点击其他地方关闭菜单
        const closeMenu = () => {
          this.showRightMsgMenu = false;
          document.removeEventListener('click', closeMenu);
        };
        document.addEventListener('click', closeMenu);
      }
    },

    // 重发消息
    async resendMessage() {
      if (this.selectedMessage) {
        const index = this.chatHistory.findIndex(msg => msg.id === this.selectedMessage.id);
        if (index > -1) {
          this.inputMessage = this.chatHistory[index].content;
          this.chatHistory.splice(index);
          if (this.currentSession) {
            this.currentSession.chatHistory = this.chatHistory;
          }
          // 删除服务端记录
          this.getCtx().chatService.deleteChatHistoryItem(this.getChatHistorySessionId(), this.selectedMessage.id);

          this.sendMessage({
            action: 'resend'
          });
        }
      }
      this.showRightMsgMenu = false;
    },

    // 编辑消息
    async editMessage() {
      if (this.selectedMessage) {
        const index = this.chatHistory.findIndex(msg => msg.id === this.selectedMessage.id);
        if (index > -1) {
          this.inputMessage = this.chatHistory[index].content;
          // 调整输入框高度
          this.$nextTick(() => {
            this.adjustTextareaHeight();
          });
          this.chatHistory.splice(index);
          if (this.currentSession) {
            this.currentSession.chatHistory = this.chatHistory;
          }
          // 删除服务端记录
          this.getCtx().chatService.deleteChatHistoryItem(this.getChatHistorySessionId(), this.selectedMessage.id);
        }
      }
      this.showRightMsgMenu = false;
    },

    // 删除消息
    async deleteMessage(clearAll) {
      if (clearAll) {
        this.chatHistory.splice(0);
        if (this.currentSession) {
          this.currentSession.chatHistory = this.chatHistory;
        }
        // 清空服务端记录
        this.getCtx().chatService.clearChatHistory(this.getChatHistorySessionId());
      }
      else {
        if (this.selectedMessage) {
          const index = this.chatHistory.findIndex(msg => msg.id === this.selectedMessage.id);
          if (index > -1) {
            this.chatHistory.splice(index);
            if (this.currentSession) {
              this.currentSession.chatHistory = this.chatHistory;
            }
            // 删除服务端记录
            this.getCtx().chatService.deleteChatHistoryItem(this.getChatHistorySessionId(), this.selectedMessage.id);
          }
        }
      }
      this.showRightMsgMenu = false;
    },
    // 调整textarea高度
    adjustTextareaHeight() {
      const textarea = this.$refs.messageInput;
      if (!textarea) return;

      // 先将高度设为auto，让scrollHeight能够正确计算
      textarea.style.height = 'auto';

      // 获取内容实际高度
      const scrollHeight = textarea.scrollHeight;

      // 获取计算后的样式
      const computedStyle = window.getComputedStyle(textarea);
      const paddingTop = parseFloat(computedStyle.paddingTop);
      const paddingBottom = parseFloat(computedStyle.paddingBottom);

      // 计算实际可用的最大高度（减去padding）
      const maxHeightWithoutPadding = this.maxTextareaHeight - paddingTop - paddingBottom;

      // 设置新高度，但不超过最大高度
      const newHeight = Math.min(scrollHeight, maxHeightWithoutPadding + paddingTop + paddingBottom);
      textarea.style.height = newHeight + 'px';

      // 只有当实际内容高度超过最大高度时才显示滚动条
      textarea.style.overflowY = scrollHeight > this.maxTextareaHeight ? 'auto' : 'hidden';
    },
    renderMarkdown(content) {
      try {
        // 检查内容是否需要markdown解析
        const hasMarkdown = /[*#\[\]`>-]/.test(content);
        const hasMarkdown_1 = /\d+[.]+\s+/.test(content); // 检查是否有有序列表
        const hasMarkdown_2 = content.indexOf('\n') !== -1;
        if (!hasMarkdown && !hasMarkdown_1 && !hasMarkdown_2) {
          return content;
        }

        return marked.parse(content);
      } catch (error) {
        console.error('Markdown rendering error:', error);
        return content;
      } finally {
        this.$nextTick(() => {
          if (this.$refs.messageContent0) {
            this.$refs.messageContent0.forEach((n) => {
              if (n.hasRepairDone == null) {
                n.hasRepairDone = true;
                this.buildChatLastOtherMessage(n);
              }
            })
          }
        })
      }
    },

    // 切换自适应模式
    toggleAdaptMode() {
      this.selfAdaptionType = this.selfAdaptionType === 0 ? 1 : 0;
      // 保存到本地存储
      localStorage.setItem('selfAdaptionType', this.selfAdaptionType);
      this.onResize();
    },
    // 处理滚动
    onScroll() {
    },
    onResize() {
      const self = this;

      let chatAreaWidth = document.documentElement.clientWidth - 250;

      // 剧中窗口
      if (self.selfAdaptionType === 0) {
        // 原有样式：宽度固定，高度自适应但与边距保持距离
        if (document.documentElement.clientWidth < self.ui.maxInnerWidth) {
          self.interactionPanelSettings.width = document.documentElement.clientWidth - self.interactionPanelSettingsLeftLeaveSpace;
        }
        else {
          self.interactionPanelSettings.width = self.ui.maxInnerWidth - self.interactionPanelSettingsLeftLeaveSpace;
        }

        // 设置原有样式
        if (self.$refs.csMiniAgent0) {
          if (self.openerName === 'cs-ai-client') {
            self.$refs.csMiniAgent0.style.height = '85vh';
          }
          else {
            self.$refs.csMiniAgent0.style.height = '90vh';
          }

          if (self.openerName === 'cs-ai-client') {
            self.$refs.csMiniAgent0.style.margin = '3vh auto';
          }
          else {
            self.$refs.csMiniAgent0.style.margin = '5vh auto';
          }

          self.$refs.csMiniAgent0.style.maxWidth = self.ui.maxInnerWidth + 'px';
          self.$refs.csMiniAgent0.style.width = '90%';
          self.$refs.csMiniAgent0.style.borderRadius = '8px';

          // 重置主内容区域样式
          const mainContent = self.$refs.csMiniAgent0.querySelector('.main-content');
          if (mainContent) {
            mainContent.style.width = '100%';
          }

          // 重置聊天区域样式
          const chatArea = self.$refs.csMiniAgent0.querySelector('.chat-area');
          if (chatArea) {
            chatArea.style.width = '';
            chatArea.style.flexGrow = '1';
          }
        }
      }
      // 贴边展开
      else if (self.selfAdaptionType === 1) {
        // 新样式：宽度和高度都自适应并贴边
        self.interactionPanelSettings.width = document.documentElement.clientWidth - self.interactionPanelSettingsLeftLeaveSpace;

        // 设置新样式
        if (self.$refs.csMiniAgent0) {
          if (self.openerName === 'cs-ai-client') {
            self.$refs.csMiniAgent0.style.height = `${document.documentElement.clientHeight - 75}px`;
          }
          else {
            self.$refs.csMiniAgent0.style.height = '100vh';
          }
          self.$refs.csMiniAgent0.style.margin = '0';
          self.$refs.csMiniAgent0.style.maxWidth = '100%';
          if (self.openerName === 'cs-ai-client') {
            self.$refs.csMiniAgent0.style.width = `${document.documentElement.clientWidth - 180}px`;
          }
          else {
            self.$refs.csMiniAgent0.style.width = '100%';
          }
          self.$refs.csMiniAgent0.style.borderRadius = '0';

          // 调整主内容区域样式，使其填满整个宽度
          const mainContent = self.$refs.csMiniAgent0.querySelector('.main-content');
          if (mainContent) {
            mainContent.style.width = '100%';
          }

          // 调整聊天区域样式，使其填满剩余空间
          const chatArea = self.$refs.csMiniAgent0.querySelector('.chat-area');
          if (chatArea) {
            chatArea.style.width = `${chatAreaWidth}px`;
            chatArea.style.flexGrow = '1';
          }
        }
      }

      self.onScroll();

      if (self.$refs.interactionPanel0) {
        self.$refs.interactionPanel0.resize({
          height: self.$refs.csMiniAgent0.clientHeight,
        });
      }
    },
  },
  async mounted() {
    const self = this;
    const ctx = this.getCtx();

    // 添加事件监听
    window.addEventListener('resize', self.onResize, false);
    window.addEventListener('scroll', self.onScroll, false);


    self.$emit('on-ready');

    if (ctx.cacShellMapper.isInShell()) {
      console.log('在CacShell中运行');
      // 改用客户端登录服务
      ctx.loginService = parent.ctx.loginService;
      self.openerName = 'cs-ai-client';

      await self.onShowed({
        selfAdaptionType: 0
      });
      self.$nextTick(function () {
        // 确保DOM已经渲染完成后应用样式
        self.onResize();
      });
    }
    // else if (this.$route.path.startsWith('/cac/app/')) {
    //   console.log('在CacShell中运行B');
    //   self.openerName = 'cs-ai-client';
    //   const cacIndexView = await waitGetObj(ctx, 'cacIndexView');
    //   // 注册
    //   await cacIndexView.registerAppStartPage({
    //     name: 'cs-mini-agent',
    //     openAppName: self.openAppName,
    //     view: self,
    //   });
    //   await self.onShowed({
    //     selfAdaptionType: 0
    //   });
    //   self.$nextTick(function () {
    //     // 确保DOM已经渲染完成后应用样式
    //     self.onResize();
    //   });
    // }
    else {
      console.log('在WebShell中运行');
      ctx.loginService = new LoginService(ctx);
      await ctx.loginService.init();

      if (self.autoShow !== false) {
        await self.onShowed();
      }
      self.$nextTick(function () {
        // 确保DOM已经渲染完成后应用样式
        self.onResize();
      });
    }

  },
  destroyed: function () {
    const self = this;
    // ...
    window.removeEventListener('resize', self.onResize, false);
    window.removeEventListener('scroll', self.onScroll, false);
  }
}
</script>

<style lang="less" scoped>
@import './assets/css/index.less';
@import './assets/css/initial-prompt.less';
</style>
