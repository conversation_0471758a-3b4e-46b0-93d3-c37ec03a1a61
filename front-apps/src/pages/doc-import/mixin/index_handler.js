import {waitGetObj} from "../../../code/util/code-util.js";

export default {
    methods: {
        async handleJsonlImport(e) {
            const inputContent = e.value;
            console.log(inputContent)
        },

        async handleTypeSelected(option) {
            this.selectedType = option.value;
            // this.placeholderText = option.placeholder;
            // console.log('选择了类型:', option);

            const contentInputJsonl0 = await waitGetObj(this.$refs, 'contentInputJsonl0')
            contentInputJsonl0.onShowed({
                placeholderText: option.placeholder,
            })
        },

        // 知识库选择相关方法
        handleKnowledgeBaseSelected(kb) {
            this.selectedKnowledgeBase = kb;
            console.log('选择了知识库:', kb);
        },

        handleKnowledgeBaseChanged(kbId) {
            this.selectedKnowledgeBaseId = kbId;
            console.log('知识库ID变更为:', kbId);
        },

        handleFileSelect(e) {
            const self = this;

            const files = Array.from(e.target.files)

            self.uploadDoc(files);
            // 清空input值，允许重复选择同一文件
            e.target.value = ''
        },

        handleDragOver(e) {
            e.preventDefault()
            e.stopPropagation()
        },

        handleDragEnter(e) {
            e.preventDefault()
            e.stopPropagation()
            this.isDragOver = true
        },

        handleDragLeave(e) {
            e.preventDefault()
            e.stopPropagation()
            this.isDragOver = false
        },

        handleDrop(e) {
            const self = this;
            e.preventDefault()
            e.stopPropagation()
            this.isDragOver = false

            const files = Array.from(e.dataTransfer.files)
            self.uploadDoc(files);
        },
    }
}