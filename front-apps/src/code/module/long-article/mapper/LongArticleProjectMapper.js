import CBAIOMapper from "../../../mapper/CBAIOMapper.js";

export class LongArticleProjectMapper {

    constructor(ctx) {
        this.ctx = ctx;
        this.cbaioMapper = new CBAIOMapper(ctx)
    }

    async getProjects(name) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/getProjects', {
            name,
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async getProjectDetails(id) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/getProjectDetails', {
            id,
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async saveProject(updType, form) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/saveProject', {
            updType, form,
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async saveProjectFields(id, fields) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/saveProjectFields', {
            id, fields
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async deleteProject(id) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/deleteProject', {
            id
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }
}