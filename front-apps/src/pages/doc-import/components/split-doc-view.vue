<template>
  <div class="split-doc-container" v-loading="ui.loading">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title">
            <el-icon class="title-icon">
              <Document />
            </el-icon>
            <h2>文档分片管理</h2>
          </div>
          <div class="stats-info">
            <div class="stat-item">
              <span class="stat-label">知识库</span>
              <span class="stat-value">{{ kbLabel }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总分片数</span>
              <span class="stat-value">{{ list.length }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总字数</span>
              <span class="stat-value">{{ totalWords }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="goBack" class="back-btn">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
          <el-button type="primary" @click="saveAll" class="save-btn">
            <el-icon>
              <Check />
            </el-icon>
            导入全部
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分片列表区域 -->
    <div class="list-area">
      <div class="list-container">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="split-item"
          :class="{ 'active': activeIndex === index }"
          @click="setActiveItem(index)"
        >
          <div class="item-header">
            <div class="item-number">{{ index + 1 }}</div>
            <div class="item-title-section">
              <div class="title-row">
                <el-input
                  v-model="item.title"
                  placeholder="请输入分片标题"
                  class="title-input"
                  @focus="setActiveItem(index)"
                />
                <div class="word-count">
                  <el-icon class="count-icon">
                    <EditPen />
                  </el-icon>
                  <span>{{ item.content.length }} 字</span>
                </div>
              </div>
            </div>
          </div>

          <div class="item-content">
            <el-input
              v-model="item.content"
              type="textarea"
              :rows="8"
              placeholder="请输入分片内容"
              class="content-textarea"
              @focus="setActiveItem(index)"
            />
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="list.length === 0" class="empty-state">
          <el-icon class="empty-icon">
            <Document />
          </el-icon>
          <p class="empty-text">暂无分片数据</p>
          <p class="empty-hint">文档分片将在这里显示</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Document,
  ArrowLeft,
  Check,
  EditPen,
  ArrowUp,
  ArrowDown,
  Delete
} from '@element-plus/icons-vue'

export default {
  name: 'split-doc-view',
  components: {
    Document,
    ArrowLeft,
    Check,
    EditPen,
    ArrowUp,
    ArrowDown,
    Delete
  },
  data() {
    return {
      list: [],
      activeIndex: -1,
      kbLabel: null,

      kb: null,

      ui: {
        loading: false,
      }
    }
  },
  computed: {
    totalWords() {
      return this.list.reduce((total, item) => total + item.content.length, 0)
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },

    async onShowed(pars) {
      const self = this;
      const { title, content, kb } = pars;

      self.kbLabel = kb.label;
      self.kb = kb;

      self.ui.loading = true;
      try {
        const t = await this.getCtx().docImportMapper.splitDoc(content);
        self.list = [];
        t.forEach((n, index) => {
          self.list.push({
            title: `${title} 分片 ${index + 1}`,
            content: n,
          })
        })
      } catch (error) {
        this.$message.error('文档分片失败：' + error.message)
      } finally {
        self.ui.loading = false;
      }
    },

    setActiveItem(index) {
      this.activeIndex = index
    },

    goBack() {
      this.$emit('on-click-back')
    },

    /**
     * 导入全部
     * @returns {Promise<void>}
     */
    async saveAll() {
      const self = this;
      try {
        self.$confirm('确定要导入到知识库吗？', '确认提示', { type: 'warning' }).then(async () => {
          // 点击确认
          const loading = self.$loading({ lock: true, text: '处理中' });
          for (const i in self.list) {
            const item = self.list[i];
            loading.setText(`处理中...${Number(i) + 1}/${self.list.length}`)
            await self.getCtx().kbDataSourceMapper.pushKf(self.kb.id, item.title, item.content);
          }
          loading.close();
          self.$alert('导入完毕', { type: 'success' });
        }).catch(() => {
          // 点击取消
        });
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      }
    },
  },

  mounted() {
  }
}
</script>

<style scoped lang="less">
.split-doc-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  .header-section {
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);

    .header-content {
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .page-title {
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            font-size: 28px;
            color: #667eea;
          }

          h2 {
            margin: 0;
            color: #2d3748;
            font-size: 24px;
            font-weight: 600;
          }
        }

        .stats-info {
          display: flex;
          gap: 24px;

          .stat-item {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .stat-label {
              font-size: 12px;
              color: #718096;
              font-weight: 500;
            }

            .stat-value {
              font-size: 20px;
              color: #2d3748;
              font-weight: 600;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        .back-btn {
          background: rgba(255, 255, 255, 0.8);
          border: 1px solid #e2e8f0;
          color: #4a5568;

          &:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #cbd5e0;
            transform: translateY(-1px);
          }
        }

        .save-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
          }
        }

        .el-button {
          padding: 12px 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          .el-icon {
            margin-right: 6px;
          }
        }
      }
    }
  }

  .list-area {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px 24px;
    scroll-behavior: smooth;

    /* 确保整个区域都能响应滚动 */
    &:hover {
      cursor: default;
    }

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(102, 126, 234, 0.3);
      border-radius: 4px;
      transition: background 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.5);
      }
    }

    .list-container {
      max-width: 1400px;
      margin: 0 auto;
      padding-right: 8px;

      /* 确保内容不会阻止滚动事件冒泡 */
      pointer-events: auto;
    }

      .split-item {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 20px;
        border: 2px solid transparent;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
          transform: scaleX(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
          border-color: rgba(102, 126, 234, 0.3);

          &::before {
            transform: scaleX(1);
          }
        }

        &.active {
          border-color: #667eea;
          box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);

          &::before {
            transform: scaleX(1);
          }
        }

        .item-header {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 20px;

          .item-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            flex-shrink: 0;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
          }

          .item-title-section {
            flex: 1;

            .title-row {
              display: flex;
              align-items: center;
              gap: 12px;

              .title-input {
                flex: 1;

                :deep(.el-input__wrapper) {
                  border-radius: 8px;
                  border: 2px solid #e2e8f0;
                  transition: all 0.3s ease;

                  &:hover {
                    border-color: #cbd5e0;
                  }

                  &.is-focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                  }
                }

                :deep(.el-input__inner) {
                  font-weight: 500;
                  color: #2d3748;
                  font-size: 16px;
                }
              }

              .word-count {
                display: flex;
                align-items: center;
                gap: 6px;
                color: #718096;
                font-size: 14px;
                white-space: nowrap;
                flex-shrink: 0;

                .count-icon {
                  font-size: 16px;
                }
              }
            }
          }
        }

        .item-content {
          margin-bottom: 16px;

          .content-textarea {
            :deep(.el-textarea__inner) {
              border-radius: 12px;
              border: 2px solid #e2e8f0;
              transition: all 0.3s ease;
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              resize: vertical;
              min-height: 160px;

              &:hover {
                border-color: #cbd5e0;
              }

              &:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
              }
            }
          }
        }

        .item-actions {
          display: flex;
          justify-content: flex-end;
          gap: 8px;

          .el-button {
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
            }

            &.el-button--danger {
              background: #fee2e2;
              border-color: #fecaca;
              color: #dc2626;

              &:hover {
                background: #fecaca;
                border-color: #f87171;
              }
            }
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
        color: #9ca3af;

        .empty-icon {
          font-size: 64px;
          margin-bottom: 20px;
          opacity: 0.6;
        }

        .empty-text {
          font-size: 18px;
          font-weight: 500;
          margin: 0 0 8px 0;
          color: #6b7280;
        }

        .empty-hint {
          font-size: 14px;
          margin: 0;
          opacity: 0.8;
        }
      }
    }
  }

/* 响应式设计 */
@media (max-width: 768px) {
  .split-doc-container {
    .header-section .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .list-area {
      padding: 16px;

      .split-item {
        padding: 16px;

        .item-header {
          flex-direction: column;
          gap: 12px;

          .item-number {
            align-self: flex-start;
          }
        }

        .item-actions {
          justify-content: center;
        }
      }
    }
  }
}
</style>