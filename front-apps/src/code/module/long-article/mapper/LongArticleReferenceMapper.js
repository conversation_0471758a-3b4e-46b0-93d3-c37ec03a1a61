import appConfig from "../../../../config/app-config.js";
import CBAIOMapper from "../../../mapper/CBAIOMapper.js";

/**
 * 长文参考资料
 */
export class LongArticleReferenceMapper {

    constructor(ctx) {
        this.ctx = ctx;
        this.cbaioMapper = new CBAIOMapper(ctx)
    }

    /**
     *
     * @param pars fileItem, projectId, understandingType
     * @param onProgressChange
     * @param onError
     * @return {Promise<void>}
     */
    async uploadDocToMd(pars, onProgressChange, onError) {
        const token = await this.ctx.loginService.getToken();
        const { fileItem, projectId, understandingType } = pars;
        const uploadUrl = `${appConfig.backRootUrl}api/la/ref-doc-to-md`;

        // 创建一个新的File对象，确保文件名正确编码
        const encodedFile = new File([fileItem.file], fileItem.file.name, {
            type: fileItem.file.type,
            lastModified: fileItem.file.lastModified
        })

        const formData = new FormData()
        formData.append('token', token)
        formData.append('projectId', projectId)
        formData.append('understandingType', understandingType)
        formData.append('file', encodedFile)
        // 额外添加原始文件名，确保服务端能正确识别
        formData.append('originalName', encodeURIComponent(fileItem.file.name))

        return await new Promise((resolve) => {
            try {
                const xhr = new XMLHttpRequest()

                // 监听上传进度
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        fileItem.progress = Math.round((e.loaded / e.total) * 100)
                        if (onProgressChange) {
                            onProgressChange({
                                progress: fileItem.progress
                            });
                        }
                    }
                })

                // 监听请求完成
                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText)
                            console.log('文件上传成功:', fileItem.name, response)

                            if (onProgressChange) {
                                onProgressChange({
                                });
                            }

                            resolve({
                                success: true,
                                fileItem, response
                            })
                        } catch (error) {
                            console.error('解析响应失败：', error)
                            // this.handleUploadError(fileItem, '响应解析失败')
                            if (onError) onError(fileItem, '响应解析失败');

                            resolve({
                                success: false,
                                msg: `解析响应失败：${error.message}`,
                            })
                        }
                    } else {
                        console.error(`上传失败 -> ${xhr.status}`, xhr.status, xhr.statusText, xhr.response)
                        if (onError) onError(fileItem, xhr.response);

                        if (xhr.response && xhr.response.startsWith('{')) {
                            resolve(JSON.parse(xhr.response))
                        }
                        else {
                            resolve({
                                success: false,
                                msg: `上传失败 -> ${xhr.status}`,
                            })
                        }
                    }
                })

                // 监听请求错误
                xhr.addEventListener('error', () => {
                    console.error('网络错误')
                    if (onError) onError(fileItem, '网络错误')
                })

                // 发送请求
                xhr.open('POST', uploadUrl)
                // 设置请求头确保中文文件名正确传输
                // xhr.setRequestHeader('Accept-Charset', 'UTF-8')
                xhr.send(formData)

            } catch (error) {
                console.error('上传异常:', error)
                if (onError) onError(fileItem, '上传异常')

                resolve({
                    success: false,
                    msg: `解析响应失败：${error.message}`,
                })
            }
        });
    }

    /**
     *
     * @param project_id
     * @return {Promise<*>}
     */
    async loadReferenceList(project_id) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/loadReferenceList', {
            project_id
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    /**
     *
     * @param project_id
     * @param name
     * @return {Promise<*>}
     */
    async deleteReference(project_id, name) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/deleteReference', {
            project_id, name
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }
}