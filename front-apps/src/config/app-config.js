let debug = false;
// 用于本地调试
let backRootUrl = 'http://localhost:3000/';
let backRealRootUrl = 'http://localhost:3000/';

// 用于本地代理融合到同域中调试
// let backRootUrl = 'http://localhost:5000/';
// let backRealRootUrl = 'http://localhost:5000/';

let openPluginUrl = './open-plugin/';
let attachmentBaseUrl = 'http://localhost:3000/';

if (process.env.NODE_ENV === 'production') {
  console.log('线上环境');
  debug = false;
  backRootUrl = '../';
  backRealRootUrl = 'http://test.ovinfo.com:2237/';
  attachmentBaseUrl = '../';
}
else if (process.env.NODE_ENV === 'test') {
  console.log('测试环境');
  debug = true;
}
else {
  console.log('开发环境');
  debug = true;
}

export default {
  // 是否开启调试
  debug,
  // 后端服务地址
  backRootUrl,
  // 后端服务地址（可直接访问的，例：http://xxx）
  backRealRootUrl,
  // 插件基础网址
  openPluginUrl,
  // 附件基础网址
  attachmentBaseUrl,
  // 百度语音识别
  baiduSpeechRecogBaseUrl: 'http://test.ovinfo.com:2243',
  baiduSpeechRecogAccessToken: '838bc71ca3a0',
}