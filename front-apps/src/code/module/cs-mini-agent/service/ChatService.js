
/**
 * 聊天服务
 */
export default class ChatService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 发送消息
     * @param {SendMessage} message 
     * @param {Object} opts
     */
    async sendMessage(message, opts) {
        const self = this

        if (opts == null) opts = {
            stream: false,
            initCb: null,
            changeCb: null, doneCb: null, onError: null, reasoningCb: null,
        };

        try {
            if (opts.stream) {
                let totalReply1 = '';
                let hasCallInitCb = false;
                return await new Promise(async function (resolve, reject) {
                    let result = null;
                    result = await self.ctx.chatMapper.sendMessageStream(message, (totalReply) => {
                        if (!hasCallInitCb) {
                            hasCallInitCb = true;
                            if (opts.initCb) {
                                opts.initCb({
                                    abortController: result.abortController,
                                    chatStreamStateId: result.chatStreamStateId,
                                })
                            }
                        }
                        totalReply1 = totalReply;
                        opts.changeCb(totalReply);
                    }, () => {
                        // if (opts.debug) console.log(`回复内容：`, totalReply1);
                        opts.doneCb(totalReply1);
                        resolve({
                            id: null,
                            reply: totalReply1,
                            time: Date.now(),
                            ...result,
                        });
                    }, opts);
                });
            }
            else {
                return await this.ctx.chatMapper.sendMessage(message, opts);
            }
        } catch(exc) {
            console.error(exc);
            return {
                id: null,
                reply: `很抱歉，请求出错了…(⊙＿⊙；)…`,
                time: Date.now(),
            };
        }
    }

    /**
     * 删除聊天会话历史记录
     * @param {*} sessionId 
     * @param {*} id 
     * @param {Object} opts
     */
    async deleteChatHistoryItem(sessionId, id, opts) {
        await this.ctx.chatMapper.deleteChatHistoryItem(sessionId, id, opts);
    }

    /**
     * 清空聊天会话历史
     * @param {*} sac 
     * @param {*} sessionId 
     * @param {*} opts 
     */
    async clearChatHistory(sessionId, opts) {
        await this.ctx.chatMapper.clearChatHistory(sessionId, opts);
    }
}