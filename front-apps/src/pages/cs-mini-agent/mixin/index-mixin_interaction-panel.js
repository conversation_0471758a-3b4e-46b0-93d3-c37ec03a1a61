export default {
    methods: {

        // 交互面板提交
        async handlerInteractionPanelSubmit(e) {
            const self = this;
            if (this.agentFrontSettings && this.agentFrontSettings.onInteractionPanelSubmit) {
                // 构建工具
                e.execAgentPlugin = async function (name, params) {
                    return await self.getCtx().agentMapper.execAgentPlugin(self.currentSession.otherMembers[0].id, name, params);
                };

                const r = await this.agentFrontSettings.onInteractionPanelSubmit(e);
                if (r !== false) {
                    this.closeInteractionPanel();
                }
            }
        },

    }
}