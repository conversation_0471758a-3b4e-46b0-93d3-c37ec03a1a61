.my-chat-list-bar {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-box {
        display: flex;
        flex-direction: row;
        padding: 10px 0;
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            outline: none;
            font-size: 14px;
            
            &::placeholder {
                color: #c0c4cc;
            }
            
            &:focus {
                border-color: #409eff;
            }
        }
    }

    .session-list {
        flex: 1;
        overflow-y: auto;
        padding: 0 10px;

        .session-item {
            padding: 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 8px;
            background: #f5f7fa;
            transition: all 0.3s;

            &:hover {
                background: #ecf5ff;
            }

            &.active {
                background: #409eff;
                color: white;
            }

            .title {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 4px;
            }

            .summary {
                font-size: 12px;
                color: #909399;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            &.active .summary {
                color: rgba(255, 255, 255, 0.8);
            }
        }

        .empty-tip {
            text-align: center;
            color: #909399;
            padding: 20px 0;
            font-size: 14px;
        }
    }
}