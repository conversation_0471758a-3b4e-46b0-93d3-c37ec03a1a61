import axios from 'axios';

/**
 * 请求GET
 * @param {*} url
 * @param {*} pars
 * @returns
 */
export async function reqGet(url, pars) {
    try {
        const response = await axios.get(url, {
            params: pars
        });
        return response.data;
    } catch (error) {
        console.error('GET request failed:', error);
        throw error;
    }
}

/**
 * 请求POST JSON
 * @param {*} url
 * @param {*} pars
 * @returns
 */
export async function reqPostJson(url, pars, opts) {
    if (opts == null) opts = {};
    try {
        const response = await axios.post(url, pars, {
            headers: {
                'Content-Type': 'application/json'
            },
            withCredentials: opts.withCredentials,
        });
        return response.data;
    } catch (error) {
        console.error('POST JSON request failed:', error);
        throw error;
    }
}

/**
 * 请求表单
 * @param {*} url
 * @param {*} pars
 * @returns
 */
export async function reqPostForm(url, pars) {
    try {
        const formData = new FormData();
        for (const key in pars) {
            formData.append(key, pars[key]);
        }
        const response = await axios.post(url, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return response.data;
    } catch (error) {
        console.error('POST Form request failed:', error);
        throw error;
    }
}

/**
 * 流式获取（用于和流式输出接口对接）
 * @param url
 * @param method
 * @param pars
 * @param cb
 * @param doneCb
 * @param errCb
 */
export async function fetchStream(url, method = 'GET', pars, cb, doneCb, errCb) {
    const abortController = new AbortController();
    const signal = abortController.signal;

    // 创建流式响应
    const response = await fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json', // 指定事件流格式
            // 'Content-Type': 'text/event-stream', // 指定事件流格式
            // 其他需要的HTTP头部
        },
        body: JSON.stringify(pars),
        // 设置为stream以获取流式响应
        // mode: 'cors',
        signal,
    })
    // 处理流式响应
    const reader = response.body.getReader();
    reader.read().then(function processStream({done, value}) {
        if (done) {
            // 流结束
            if (doneCb) doneCb();
        } else {
            // 处理收到的数据
            const utf8Decoder = new TextDecoder("utf-8");
            let raw = value ? utf8Decoder.decode(value, {stream: true}) : '';
            if (cb) cb(raw);

            // 递归读取下一个chunk
            reader.read().then(processStream);
        }
    });

    return {
        abortController
    }

    // // 创建流式响应
    // fetch(url, {
    //     method: method,
    //     headers: {
    //         'Content-Type': 'application/json', // 指定事件流格式
    //         // 'Content-Type': 'text/event-stream', // 指定事件流格式
    //         // 其他需要的HTTP头部
    //     },
    //     body: JSON.stringify(pars),
    //     // 设置为stream以获取流式响应
    //     // mode: 'cors',
    //     signal,
    // }).then((response) => {
    //
    //     // 处理流式响应
    //     const reader = response.body.getReader();
    //     reader.read().then(function processStream({done, value}) {
    //         if (done) {
    //             // 流结束
    //             if (doneCb) doneCb();
    //         } else {
    //             // 处理收到的数据
    //             const utf8Decoder = new TextDecoder("utf-8");
    //             let raw = value ? utf8Decoder.decode(value, {stream: true}) : '';
    //             if (cb) cb(raw);
    //
    //             // 递归读取下一个chunk
    //             reader.read().then(processStream);
    //         }
    //     });
    // }).catch((err) => {
    //     if (errCb) errCb(err);
    // });
}