<template>
  <div class="content-input_jsonl" style="flex-grow: 1;">
    <div class="input-container" :class="{ 'drag-over': isDragOver }">
      <el-input
          v-model="value"
          :rows="30"
          type="textarea"
          :placeholder="placeholderText"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragenter="handleDragEnter"
          @dragleave="handleDragLeave"
      />
      <div v-if="isDragOver" class="drag-overlay">
        <div class="drag-content">
          <i class="el-icon-upload2"></i>
          <p>释放文件以导入内容</p>
        </div>
      </div>
    </div>
    <div style="width: 100%;height: 10px;"></div>
    <div style="text-align: center;">
      <el-button type="primary" @click="handleImport">导入</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'content-input_jsonl',
  props: {
  },
  data() {
    return {
      value: null,
      placeholderText: null,
      isDragOver: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    onShowed(pars = {}) {
      this.placeholderText = pars.placeholderText || ''
    },
    getValue() {
      return this.value;
    },
    async import(pars) {
      const self = this;
      const { kbId, list } = pars;

      if (kbId == null || kbId.trim().length === 0) {
        this.$alert('请选择一个知识库', { type: 'error' });
        return;
      }

      self.$confirm('确定要导入到知识库吗？', '确认提示', { type: 'warning' }).then(async () => {
        // 点击确认
        const loading = self.$loading({ lock: true, text: '处理中' });
        for (const i in list) {
          const item = list[i];
          loading.setText(`处理中...${Number(i) + 1}/${list.length}`)
          // await self.getCtx().kbDataSourceMapper.pushKf(kbId, item.title, item.content);
        }
        loading.close();
        self.$alert('导入完毕', { type: 'success' });
      }).catch(() => {
        // 点击取消
      });
    },
    async handleImport() {
      if (this.value == null || this.value.trim().length === 0) {
        this.$alert('导入内容不能为空', { type: 'error' });
        return;
      }

      // 解析内容
      const list = [];
      this.value.split('\n').forEach((n) => {
        if (n && n.trim().length > 0) {
          list.push(JSON.parse(n));
        }
      })

      this.$emit('on-import', {
        value: this.value,
        list: list,
      })
    },


    handleDragOver(e) {
      e.preventDefault()
      e.stopPropagation()
    },

    handleDragEnter(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = true
    },

    handleDragLeave(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false
    },

    handleDrop(e) {
      const self = this;
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false


      const files = Array.from(e.dataTransfer.files)
      if (files.length > 0) {
        const file = files[0];
        const reader = new FileReader();

        reader.onload = (e) => {
          self.value = e.target.result;
        };
        reader.readAsText(file);
      }
    },
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.content-input_jsonl {
  .input-container {
    position: relative;
    transition: all 0.3s ease;

    &.drag-over {
      transform: scale(1.02);

      /deep/ .el-textarea__inner {
        border: 2px dashed #409EFF !important;
        background-color: rgba(64, 158, 255, 0.05) !important;
        box-shadow: 0 0 10px rgba(64, 158, 255, 0.3) !important;
      }
    }

    .drag-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(64, 158, 255, 0.1);
      border: 2px dashed #409EFF;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      pointer-events: none;

      .drag-content {
        text-align: center;
        color: #409EFF;
        font-size: 16px;

        i {
          font-size: 48px;
          margin-bottom: 10px;
          display: block;
        }

        p {
          margin: 0;
          font-weight: 500;
        }
      }
    }
  }
}
</style>