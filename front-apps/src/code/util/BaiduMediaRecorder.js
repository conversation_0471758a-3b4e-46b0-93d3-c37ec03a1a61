export function BaiduMediaRecorder(cfg) {
    let mediaRecorder;
    let audioChunks = [];
    let stream;

    let baseUrl = '';
    let accessToken = '';

    {
        if (cfg.baseUrl) baseUrl = cfg.baseUrl;
        if (cfg.accessToken) accessToken = cfg.accessToken;
    }

    this.start = async function () {
        if (navigator.mediaDevices) {
            audioChunks = [];
            stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);
    
            mediaRecorder.ondataavailable = (event) => {
                audioChunks.push(event.data);
            };
    
            mediaRecorder.start();
        }
        else {
            alert('当前环境不支持此功能');
            return false;
        }
    };

    this.stop = async function (cb, errCb) {
        mediaRecorder.stop();
        stream.getTracks().forEach(track => track.stop());

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });

            const audioContext = new (window.AudioContext || window.webkitAudioContext)();

            const arrayBuffer = await audioBlob.arrayBuffer();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

            const pcmData = convertToPCM(audioBuffer);
            const pcmBlob = new Blob([pcmData], { type: 'audio/pcm' });

            const formData = new FormData();
            formData.append('audio', pcmBlob);

            const response = await fetch(`${baseUrl}/recognize?token=${accessToken}`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.error) {
                if (errCb) errCb(result.error);
                throw new Error(result.error);
            }

            if (cb) cb(result.text);
        };
    };


    function convertToPCM(audioBuffer) {
        const numChannels = 1;
        const sampleRate = 16000;
        const bytesPerSample = 2;

        const originalBuffer = audioBuffer.getChannelData(0);
        const ratio = audioBuffer.sampleRate / sampleRate;
        const newLength = Math.round(originalBuffer.length / ratio);
        const result = new Float32Array(newLength);

        for (let i = 0; i < newLength; i++) {
            result[i] = originalBuffer[Math.floor(i * ratio)];
        }

        const pcmBuffer = new Int16Array(newLength);
        for (let i = 0; i < newLength; i++) {
            const s = Math.max(-1, Math.min(1, result[i]));
            pcmBuffer[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }

        return pcmBuffer;
    }
}