<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    :before-close="handleClose"
    @closed="handleClosed"
  >
    <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;">
      <el-button size="small" @click="handleExport">导出</el-button>
      <el-button size="small" @click="handleImport" style="margin-left: 8px;">导入</el-button>
    </div>
    <el-form :model="form" :rules="rules" ref="ucaFormRef" label-width="80px" v-loading="loading">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="简单介绍" prop="summary">
        <el-input v-model="form.summary" placeholder="用于在列表名称下方显示"></el-input>
      </el-form-item>
      <el-form-item label="具体描述" prop="systemPrompt">
        <el-input
          type="textarea"
          v-model="form.systemPrompt"
          :rows="20"
          placeholder="用于描述专员的角色和行为等"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="hide">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'UcaFormWin',
  data() {
    return {
      visible: false,
      isEdit: false,
      loading: false,
      updType: 0, // 0-新增，1-编辑
      form: {
        id: '', // 编辑时使用
        name: '',
        summary: '',
        systemPrompt: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入专员名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        // systemPrompt: [
        //   { required: true, message: '请输入系统提示词', trigger: 'blur' }
        // ],
      },
      onSubmitDoneCallback: null,
    }
  },
  computed: {
    dialogTitle() {
      return this.updType === 0 ? '新增专员' : '编辑专员'
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    /**
     * 显示弹框
     * @param {Object} data 编辑时传入的数据
     * @param {*} data.updType 0-新增，1-编辑
     * @param {*} data.agentId 专员id
     */
    async show(data) {
      this.visible = true
      this.resetForm()

      // 设置updType
      if (data && data.updType !== undefined) {
        this.updType = data.updType
      } else {
        this.updType = 0 // 默认为新增
      }

      this.onSubmitDoneCallback = data.onSubmitDoneCallback;
      
      // 如果是编辑模式且有agentId，则加载表单数据
      if (this.updType === 1 && data && data.agentId) {
        try {
          // 加载表单数据
          this.loading = true;
          const formData = await this.getCtx().userCreateAgentMapper.getMyAgentForm(
            data.agentId
          )
          this.loading = false;
          
          if (formData) {
            this.isEdit = true
            this.form.id = data.agentId
            this.form.name = formData.name || ''
            this.form.summary = formData.summary || ''
            this.form.systemPrompt = formData.systemPrompt || ''
          }
        } catch (error) {
          console.error('加载专员表单数据失败:', error)
          ElMessage.error('加载专员表单数据失败: ' + error.message)
          this.hide()
        }
      } else {
        this.isEdit = false
      }
    },
    
    /**
     * 隐藏弹框
     */
    hide() {
      this.visible = false
    },
    
    /**
     * 关闭前的回调
     */
    handleClose(done) {
      done()
    },
    
    /**
     * 弹框关闭后的回调
     */
    handleClosed() {
      this.resetForm()
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      if (this.$refs.ucaFormRef) {
        this.$refs.ucaFormRef.resetFields()
      }
      this.form.id = ''
      this.form.name = ''
      this.form.summary = ''
      this.form.systemPrompt = ''
    },
    
    /**
     * 提交表单
     */
    async submitForm() {
      const self = this;
      if (!this.$refs.ucaFormRef) return
      
      this.$refs.ucaFormRef.validate(async (valid) => {
        if (valid) {
          try {
            // 准备表单数据
            const formData = {
              name: this.form.name,
              summary: this.form.summary,
              systemPrompt: this.form.systemPrompt
            }
            
            try {
              this.loading = true;
              await this.getCtx().userCreateAgentMapper.saveMyAgentForm(
                this.form.id || '', // 新增时为空字符串
                formData
              )
              this.loading = false;
            } catch (err) {
              console.error('保存失败详情:', err)
              throw err
            }

            // 显示成功提示
            ElMessage.success(`${this.isEdit ? '编辑' : '新增'}专员成功`)
            
            // 关闭弹框
            this.hide()

            if (this.onSubmitDoneCallback) {
              this.onSubmitDoneCallback();
            }
          } catch (error) {
            console.error(`${this.isEdit ? '编辑' : '新增'}专员失败:`, error)
            ElMessage.error(`${this.isEdit ? '编辑' : '新增'}专员失败: ` + error.message)
          }
        } else {
          return false
        }
      })
    },
    /**
     * 导出表单到剪贴板
     */
    async handleExport() {
      try {
        const { id, ...data } = this.form;
        const json = JSON.stringify(data, null, 2);
        await navigator.clipboard.writeText(json);
        ElMessage.success('已导出到剪贴板');
      } catch (e) {
        ElMessage.error('导出失败: ' + (e.message || e));
      }
    },
    /**
     * 从剪贴板导入表单
     */
    async handleImport() {
      const self = this;
      this.$confirm('确定要从剪贴板中导入吗？', '确认提示', { type: 'warning' }).then(async () => {
        // 点击确认
        try {
          const text = await navigator.clipboard.readText();
          const data = JSON.parse(text);
          if (typeof data !== 'object' || !data) throw new Error('剪贴板内容不是有效的JSON');
          // 只赋值允许的字段
          self.form.name = data.name || '';
          self.form.summary = data.summary || '';
          self.form.systemPrompt = data.systemPrompt || '';
          ElMessage.success('导入成功');
        } catch (e) {
          ElMessage.error('导入失败: ' + (e.message || e));
        }
      }).catch(() => {
        // 点击取消
      });
    },
  }
}


</script>

<style scoped lang="less">
.el-dialog__body {
  padding: 20px 20px 0;
}

.el-textarea__inner {
  font-family: inherit;
}
</style>
