<template>
    <div class="speech-input-float-panel" v-if="visible">
        <div class="overlay" @click="handleClose"></div>
        <div class="panel-content">
            <div class="mic-container">
                <div class="mic-icon" :class="{ 'recording': isRecording }">
                    <i class="el-icon-microphone"></i>
                </div>
                <div class="status-text">{{ statusText }}</div>
            </div>
            <div class="action-buttons">
                <button class="action-btn" @click="handleSend">发送</button>
                <button class="action-btn" @click="handleEdit">编辑</button>
                <button class="action-btn" @click="handleAdd">添加</button>
            </div>
            <div class="control-buttons">
                <button class="close-btn" @click="handleClose">
                    <img src="../assets/img/close.svg" alt="关闭" class="close-icon">
                </button>
            </div>
        </div>
    </div>
</template>

<script>
// import CACShellMapper from '../../../code/mapper/CACShellMapper.js';
import {BaiduMediaRecorder} from '../../../code/util/BaiduMediaRecorder';
import appConfig from '../../../config/app-config';
/**
 * 语音输入浮动面板
 * 当点击输入框上的麦克风图标时，显示此浮动面板，用于收集用户说话内容
 * 背景是个透明遮罩，中间区域是个麦克风图标，下面有个关闭按钮
 */
export default {
    name: 'SpeechInputFloatPanel',
    data() {
        return {
            bmr: null,
            visible: false,
            isRecording: true,
            statusText: '正在录音...'
        }
    },
    methods: {
        newRecorder() {
            // this.cacShellMapper = new CACShellMapper();
            if (this.cacShellMapper.isInClient()) {
                console.log('使用客户端录音器');
                return this.cacShellMapper.newBaiduMediaRecorder();
            }
            console.log('使用浏览器录音器');
            return new BaiduMediaRecorder({
                baseUrl: appConfig.baiduSpeechRecogBaseUrl,
                accessToken: appConfig.baiduSpeechRecogAccessToken
            });
        },
        // 显示面板
        async show() {
            this.bmr = this.newRecorder();
            const r = await this.bmr.start();
            if (r === false) return false;

            this.visible = true;
            this.isRecording = true;
            this.statusText = '正在录音...';
        },
        // 设置状态文本
        setStatusText(text) {
            this.statusText = text;
        },
        // 设置录音状态
        setRecording(isRecording) {
            this.isRecording = isRecording;
        },
        // 修复最后的文本
        repairFinalText(text) {
            if (text) {
                // 删除末尾的中文句号
                text = text.replace(/\s*。$/, '');
            }
            return text;
        },
        // 关闭面板
        handleClose() {
            this.visible = false;
            this.$emit('close');
            this.bmr.stop();
        },
        handleSend() {
            // 发送逻辑待实现
            this.statusText = '识别中...';
            this.bmr.stop((text) => {
                this.$emit('on-send', this.repairFinalText(text));
                this.handleClose();
            }, (error) => {
                this.statusText = '识别失败';
                console.error('识别失败:', error);
            })
        },
        handleEdit() {
            // 编辑逻辑待实现
            this.statusText = '识别中...';
            this.bmr.stop((text) => {
                this.$emit('on-edit', this.repairFinalText(text));
                this.handleClose();
            }, (error) => {
                this.statusText = '识别失败';
                console.error('识别失败:', error);
            })
        },
        handleAdd() {
            // 添加逻辑待实现
            this.statusText = '识别中...';
            this.bmr.stop((text) => {
                this.$emit('on-add', this.repairFinalText(text));
                this.handleClose();
            }, (error) => {
                this.statusText = '识别失败';
                console.error('识别失败:', error);
            })
        }
    }
}
</script>

<style scoped lang="less">
.speech-input-float-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;

    .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .panel-content {
        position: relative;
        width: 300px;
        padding: 30px;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1;
    }

    .mic-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 10px;

        .mic-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            transition: all 0.3s ease;

            i {
                font-size: 40px;
                color: #666;
            }

            &.recording {
                background-color: #1890ff;
                animation: pulse 1.5s infinite;

                i {
                    color: white;
                }
            }
        }

        .status-text {
            text-align: center;
            color: #666;
            font-size: 14px;
            min-height: 40px;
            max-width: 250px;
            word-break: break-word;
        }
    }

    .action-buttons {
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-bottom: 30px;

        .action-btn {
            padding: 8px 20px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;

            &:hover {
                background-color: #e0e0e0;
            }
        }
    }

    .control-buttons {
        width: 100%;
        display: flex;
        justify-content: center;

        .close-btn {
            width: 30px;
            height: 30px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0;
            transition: all 0.2s;

            &:hover {
                background-color: #e0e0e0;
            }

            .close-icon {
                width: 10px;
                height: 10px;
            }
        }
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
}
</style>
