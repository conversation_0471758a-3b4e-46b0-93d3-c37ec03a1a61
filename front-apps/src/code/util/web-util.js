/**
 * 获取cookie
 * @param {*} cookieName 
 * @returns 
 */
export function getCookie(cookieName) {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === cookieName) {
            return value;
        }
    }
    return null;
}

/**
 * 设置cookie
 * @param {*} cookieName 
 * @param {*} value 
 * @param {Date} expirationDate 
 */
export function setCookie(cookieName, value, expirationDate) {
    document.cookie = `${cookieName}=${value}; expires=${expirationDate.toUTCString()}; path=/`;
}

/**
 * 删除cookie
 * @param {*} cookieName 
 */
export function delCookie(cookieName) {
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`;
}

/**
 * 获取url参数
 * @param {*} name 
 * @param {*} _window 
 * @returns 
 */
export function queryString(name, _window) {
    let t_window = null;
    if (_window != null) {
        t_window = _window;
    } else {
        t_window = window;
    }

    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    const r = t_window.location.search.substr(1).match(reg);
    if (r != null)
        return r[2]; /*unescape(r[2]);*/
    return '';
}