{"name": "vue3-vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev_frontend": "cross-env NODE_ENV=development VITE_BASE_URL=/front-apps/ vite", "build-copy-to-cbaio": "dev-build-copy-to.bat", "build": "vite build", "dev": "vite"}, "devDependencies": {"@types/node": "^20.5.7", "@vitejs/plugin-vue": "^5.2.1", "@rollup/plugin-babel": "^6.0.3", "babel-plugin-component": "^1.1.1", "less": "^4.2.0", "less-loader": "^11.1.3", "vite": "^6.2.6"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@logicflow/core": "^2.0.12", "@logicflow/extension": "^2.0.16", "axios": "^1.6.2", "cross-env": "^7.0.3", "element-plus": "^2.9.5", "marked": "^15.0.6", "pinia": "^2.1.4", "vue": "^3.5.13", "vue-draggable-next": "^2.2.1", "vue-router": "^4.5.0"}}