export default {
    data() {
        return {
            isSACSessionSelected: false,
        }
    },
    methods: {
        checkIsSACSessionSelected() {
            this.isSACSessionSelected = this.$refs.myChatListBar0.isMyAgentSelected();
        },
        // 新增专员
        handleAddAgent() {
            const self = this;
            // 打开自定义专员表单弹框
            this.$refs.ucaFormWin0.show({
                ctx: this.getCtx(),
                updType: 0,
                onSubmitDoneCallback() {
                    self.$refs.myChatListBar0.loadDataAll()
                },
            });
        },
        
        // 编辑专员
        async handleEditAgent(agentId) {
            const self = this;
            try {
                // 打开自定义专员表单弹框，传入编辑模式和专员ID
                this.$refs.ucaFormWin0.show({
                    ctx: this.getCtx(),
                    updType: 1,
                    agentId: agentId,
                    onSubmitDoneCallback() {
                        self.$refs.myChatListBar0.loadDataAll()
                    }
                });
            } catch (error) {
                console.error('编辑专员失败:', error);
                this.$message.error('编辑专员失败: ' + error.message);
            }
        },

        // 删除专员
        async handleDeleteAgent(agentId) {
            const self = this;
            self.$confirm('确定要删除此专员吗？', '确认提示', { type: 'warning' }).then(async () => {
                // 点击确认
                self.loading = true;
                await self.getCtx().userCreateAgentMapper.deleteMyAgent(agentId);
                self.loading = false;
                self.$refs.myChatListBar0.loadDataAll();
                self.currentSession = null;
                self.isSACSessionSelected = false;
            }).catch(() => {
                // 点击取消
            });
        },
    }
}