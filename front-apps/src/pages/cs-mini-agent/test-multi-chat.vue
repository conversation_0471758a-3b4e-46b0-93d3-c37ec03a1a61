<template>
<div class="test-multi-chat" v-loading="loading">
  <div class="chat-output-area">
    <template v-for="(chatContainer, index) in chatContainerList">
      <div v-if="index > 0" class="v-split-line"></div>
      <div class="chat-container">
        <div class="header">
          {{chatContainer.title}}
        </div>
        <div class="body">
          <chat-message-list :ref="`cml_${chatContainer.id}`" :messages="chatContainer.messages"></chat-message-list>
        </div>
      </div>
      <div v-if="index === chatContainerList.length - 1" class="v-split-line"></div>
    </template>
  </div>
  <div class="chat-input-area">
    <el-input
        v-model="chatInputText"
        style="width: 600px"
        :rows="6"
        type="textarea"
        placeholder="请输入内容，按回车发送"
        @keydown.enter.prevent="handleEnterKey" :disabled="isReplying"
    />
  </div>
</div>
</template>

<script>
import ChatMessageList from "./components/chat-message-list.vue";
import LoginService from "../../code/module/platform/service/LoginService.js";
import LoginViewModel from "../../code/module/platform/view-model/LoginViewModel.js";
import ChatService from "../../code/module/cs-mini-agent/service/ChatService.js";
import AgentMapper from "../../code/module/cs-mini-agent/mapper/AgentMapper.js";
import ChatMapper from "../../code/module/cs-mini-agent/mapper/ChatMapper.js";

export default {
  // 多模型聊天测试
  name: "test-multi-chat",
  components: {ChatMessageList},
  data() {
    return {
      debug: true,
      loading: false,
      receiverId: 'yxt.cn_chat-model-lhgc',
      sessionId: 'agent-lhgc',
      chatContainerList: [],
      chatInputText: '',
      isReplying: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    getChatContainerList() {
      const list = [
        { title: 'Qwen3-32b（公司）', model: 'ollama_qwen3:32b|no_think|' },
        { title: 'Qwen3-235b', model: 'albl_qwen3-235b-a22b|no_think|' },
        { title: 'DeepSeek-V3-满血（官网）', model: 'ds_deepseek-chat' },
        { title: 'DeepSeek-V3-满血（ALiBL）', model: 'albl_deepseek-v3' },
        { title: 'GPT-4o', model: 'br_gpt-4o' },
        { title: 'GLM-4-plus', model: 'zhipu_glm-4-plus' },
        { title: 'MiniMax-Text-01', model: 'minimax_MiniMax-Text-01' },
      ];

      for (let i = 0; i < list.length; i++) {
        list[i].id = String(i);
        list[i].messages = [];
        list[i].isReplying = false;
      }

      return list;
    },
    async onShowed(pars = {}) {
      const self = this;

      self.loading = true;

      let ctx = this.getCtx();
      if (ctx.loginService == null) {
        ctx.loginService = new LoginService(ctx);
        await ctx.loginService.init();
      }
      if (ctx.loginViewModel == null) ctx.loginViewModel = new LoginViewModel(ctx, { self });
      if (ctx.chatService == null) ctx.chatService = new ChatService(ctx);
      if (ctx.agentMapper == null) ctx.agentMapper = new AgentMapper(ctx);
      if (ctx.chatMapper == null) ctx.chatMapper = new ChatMapper(ctx);

      // 登录检查
      await ctx.loginViewModel.checkLogin({
        readyCb() {
          self.loading = false
        },
        cb(pars1) {
          // self.loginUserRealname = pars1.loginUser.realname;
        }
      });

      self.loading = false;

      self.chatContainerList = self.getChatContainerList();
    },
    async sendMessage(chatContainer, doneCb) {
      const self = this;

      const chatMessageListRef = self.$refs[`cml_${chatContainer.id}`][0];
      chatContainer.isReplying = true;

      chatContainer.messages.push({
        role: 'user',
        content: self.chatInputText,
      });

      const sendMessage = {
        id: Date.now().toString(),
        content: self.chatInputText,
        sessionId: self.sessionId,
        senderId: (await this.getCtx().loginService.getUser()).username,
        receiverId: self.receiverId,
        time: Date.now(),
        opts: {
        },
      };

      chatMessageListRef.isThinking(true);
      const receiveMessage = await this.getCtx().chatService.sendMessage(sendMessage, {
        debug: self.debug,
        chatModel: chatContainer.model,
        stream: true,
        reasoningCb: (totalReasoning) => {
        },
        changeCb: (totalReply) => {
          chatMessageListRef.setTextToLastLeftMessage(totalReply);
        },
        doneCb: (totalReply) => {
          chatMessageListRef.setTextToLastLeftMessage(totalReply);
          chatContainer.isReplying = false;
          chatMessageListRef.isThinking(false);
          if (doneCb) doneCb();
        },
        thinkStateChangeCb: (val) => {
        },
      });
    },
    async handleEnterKey(e) {
      const self = this;
      self.isReplying = true;

      function isAllReplyDone() {
        for (const chatContainer of self.chatContainerList) {
          if (chatContainer.isReplying) {
            return false;
          }
        }
        return true;
      }

      for (const chatContainer of self.chatContainerList) {
        chatContainer.messages.splice(0);
        self.sendMessage(chatContainer, () => {
          if (isAllReplyDone()) {
            self.isReplying = false;
            self.chatInputText = '';
          }
        });
      }
    }
  },
  mounted() {
    this.onShowed();
  }
}
</script>

<style scoped lang="less">
.test-multi-chat {
  height: 100%;
  display: flex;
  flex-direction: column;

  .chat-output-area {
    flex-grow: 1;
    display: flex;
    flex-direction: row;

    /*分割线*/
    .v-split-line {
      width: 3px;
      height: 100%;
      background-color: #676767;
    }

    /*对话容器，用于展示对话内容*/
    .chat-container {
      width: 600px;
      display: flex;
      flex-direction: column;

      /*对话容器标题*/
      .header {
        line-height: 50px;
        text-align: center;
      }

      .body {
        flex-grow: 1;
        height: 0;
        overflow-y: auto;
      }
    }
  }

  .chat-input-area {
    min-height: 100px;
    text-align: center;
    padding: 20px 0 20px 0;
  }
}
</style>