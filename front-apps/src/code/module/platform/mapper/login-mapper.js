import { reqP<PERSON><PERSON><PERSON> } from "../../../util/http-util.js";
import sysCfg from "../../../../config/app-config.js";
import CSDESA from "../../../util/CSDESA.js";

/**
 * 判断是否登录
 * @param {String} token 登录令牌
 * @returns {Boolean} 是否登录
 */
export async function isLogin(token, opts) {
    return await reqPostJson(`${sysCfg.backRootUrl}api/user/isLogin`, {
        token: token,
        ...opts,
    });
    // if (!r.success) {
    //     throw new Error(r.msg);
    // }
    // return r.data;
}

/**
 * 登录
 * @param {String} sac SAC
 * @param {String} username 用户名
 * @param {String} password 密码
 * @param opts
 * @returns {LoginResult} 登录结果
 */
export async function login(sac, username, password, opts) {
    const tokenStr = `${username}|${password}|${sac}|${Date.now()}`;
    const csdesa = new CSDESA();
    const token_348bdbde = csdesa.encrypt('!a3d@cde', tokenStr);
    const r = await reqPostJson(`${sysCfg.backRootUrl}api/user/login`, {
        token_348bdbde,
        // sac: sac,
        // username: username,
        // password: password,
        ...opts,
    });
    return r;
}

export async function loginByRn(sac, username, skey, opts) {
    const r = await reqPostJson(`${sysCfg.backRootUrl}api/user/login`, {
        sac: sac,
        username: username,
        skey: skey,
        ...opts,
    });
    // if (!r.success) {
    //     throw new Error(r.msg);
    // }
    return r;
}

/**
 * 登出
 * @param {*} sac 
 * @param {*} username 
 * @param {*} password 
 * @param {*} opts 
 * @returns 
 */
export async function logout(sac, username, opts) {
    const r = await reqPostJson(`${sysCfg.backRootUrl}api/user/logout`, {
        sac: sac,
        username: username,
        ...opts,
    });
    // if (!r.success) {
    //     throw new Error(r.msg);
    // }
    return r;
}