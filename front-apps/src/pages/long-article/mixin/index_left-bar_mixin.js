export default {
    data() {
        return {

        }
    },
    methods: {
        async handleLeftSearch() {
            this.loading = true;
            try {
                await this.loadProjects();
            } catch (exc) {
                await this.$alert(exc.message, { type: 'error' });
            }
            this.loading = false;
        },
        handleSelectLeftItem(item) {
            this.projects.forEach((n) => {
                n.selected = null;
            })
            item.selected = true;
            this.selectedProject = item;

            this.$refs.projectView0.onShowed({
                ctx: this.getCtx(),
                project: this.selectedProject,
            })
        },
        async handleDeleteLeftItem(item) {
            const self = this;
            self.$confirm('确定要删除吗？', '确认提示', { type: 'warning' }).then(async () => {
                // 点击确认
                self.loading = true;
                try {
                    await self.getCtx().longArticleProjectMapper.deleteProject(item.id);
                } catch (exc) {
                    await self.$alert(exc.message, { type: 'error' });
                }
                await self.loadProjects();
                self.loading = false;
            }).catch(() => {
                // 点击取消
            });
        }
    }
}