import { isLogin, login, loginByRn, logout } from "../mapper/login-mapper.js";
import { getCookie, setCookie, delCookie } from "../../../util/web-util.js";
/**
 * 登录服务
 */
export default class LoginService {
    constructor(ctx) {
        this.ctx = ctx;
        // 登录令牌
        this.token = null;
        // 用户信息
        this.user = null;

        // this.token = await this.loadClientData();
    }

    async init() {
        this.token = await this.loadClientData();
    }

    async getToken() {
        return this.token;
    }

    async getUserId() {
        if (this.user) {
            return this.user.id;
        }
    }

    async getUser() {
        return this.user;
    }

    /**
     * 判断是否登录
     * @returns {Boolean} 是否登录
     */
    async isLogin() {
        const r = await isLogin(this.token, {
        });
        if (!r.success) {
            throw new Error(r.msg);
        }

        const data = r.data;
        if (data.isLogin) {
            this.user = data.user;
        }
        else {
            this.user = null;
        }
        return data.isLogin;
    }

    /**
     * 登录
     * @param {String} sac 授权号
     * @param {String} username 用户名
     * @param {String} password 密码
     * @returns {LoginResult} 登录结果
     */
    async login(sac, username, password) {
        const r = await login(sac, username, password);
        if (r.success) {
            this.token = r.token;
            this.user = r.user;

            this.saveClientData(r.token);
        }
        return r;
    }

    /**
     * 
     * @param {*} sac 
     * @param {*} username 
     * @param {*} skey 
     * @returns 
     */
    async loginByUn(sac, username, skey) {
        const r = await loginByRn(sac, username, skey);
        if (r.success) {
            this.token = r.token;
            this.user = r.user;

            this.saveClientData(r.token);
        }
        return r;
    }

    async loginByToken(token) {
        const r = await isLogin(token, {
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
        if (r.data.isLogin) {
            this.token = token;
            this.user = r.data.user;
        }
        return r;
    }

    /**
     * 登出
     * @returns 
     */
    async logout() {
        let sac;
        let username;

        if (this.user) {
            sac = this.user.sac;
            username = this.user.username;
        }

        const r = await logout(sac, username);
        this.clearClientData();
        return r;
    }


    // 加载客户端数据
    async loadClientData() {
        return await this.ctx.clientDataService.get('token')

        // 从cookie中获取token（使用原生cookie接口）
        // return getCookie('token');
    }

    // 保存客户端数据
    saveClientData(token) {
        this.ctx.clientDataService.set('token', token)

        // // 存放token到cookie中（使用原生cookie接口）
        // const expirationDate = new Date();
        // expirationDate.setDate(expirationDate.getDate() + 1000); // token有效期n天
        // setCookie('token', token, expirationDate);
    }

    // 清除客户端数据
    clearClientData() {
        this.ctx.clientDataService.remove('token')

        // delCookie('token');
    }
}