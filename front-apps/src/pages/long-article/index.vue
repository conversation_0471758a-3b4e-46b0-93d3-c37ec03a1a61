<template>
  <div class="long-article app-start-view_left-right" v-loading="loading" :update-code="updateCode">
    <!-- 左栏 -->
    <div class="left-bar" v-show="hideLeftBar !== true">
      <div class="search-bar">
        <div class="search-input-wrapper">
          <el-input 
            v-model="query.kw" 
            placeholder="搜索项目..."
            clearable
            @keyup.enter="handleLeftSearch"
          ></el-input>
        </div>
        <el-button 
          type="primary"
          @click="handleLeftSearch"
          class="search-btn"
        >
          搜索
        </el-button>
      </div>
      <div class="left-list-area">
        <div class="project-list">
          <template v-for="item in projects" :key="item.id">
            <div :class="{ 'list-item': true, 'list-item_selected': item.selected }">
              <div class="label-area" @click="handleSelectLeftItem(item)">
                <div class="icon">
                  <el-icon class="project-icon">
                    <Document />
                  </el-icon>
                </div>
                <div class="label">
                  <span class="project-name">{{ item.name }}</span>
<!--                  <span class="project-meta">{{ item.summary }}</span>-->
                </div>
              </div>
              <div class="ctrl-area" v-if="item.selected">
                <img src="../../assets/img/delete-white.svg" title="删除" @click="handleDeleteLeftItem(item)" />
              </div>
            </div>
          </template>
        </div>
        <!-- 新增项目 -->
        <div class="add-item" @click="handleNewProject">
          <el-icon class="plus-icon">
            <Plus />
          </el-icon>
          <span class="add-text">创建新项目</span>
        </div>
      </div>
    </div>

    <!-- 分隔栏 -->
    <div class="separator-bar-v" v-show="hideLeftBar !== true"></div>

    <!-- 主内容区域 -->
    <div class="main-content" v-show="selectedProject != null">
      <index_project-view ref="projectView0"></index_project-view>
    </div>
    
    <!-- 空状态 -->
    <div class="empty-state" v-show="selectedProject == null">
      <div class="empty-content">
        <el-icon class="empty-icon">
          <Document />
        </el-icon>
        <h3 class="empty-title">选择一个项目开始工作</h3>
        <p class="empty-description">从左侧选择一个项目，或者创建一个新的项目来开始您的长文章创作之旅</p>
        <el-button type="primary" @click="handleNewProject" class="create-project-btn">
          <el-icon><Plus /></el-icon>
          创建新项目
        </el-button>
      </div>
    </div>

    <!-- 项目表单弹窗 -->
    <el-dialog 
      :title="`${projectFormMode === 0 ? '创建' : '编辑'}项目`" 
      v-model="projectFormDialogVisible" 
      width="600px"
      :close-on-click-modal="false"
      class="project-dialog"
    >
      <div v-loading="loading">
        <div class="dialog-body">
          <el-form :model="projectForm" label-width="100px" label-position="left">
            <el-form-item label="项目名称" required>
              <el-input 
                v-model="projectForm.name" 
                placeholder="请输入项目名称" 
                maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
<!--            <el-form-item label="项目描述">-->
<!--              <el-input -->
<!--                v-model="projectForm.description" -->
<!--                type="textarea" -->
<!--                placeholder="请输入项目描述（可选）" -->
<!--                :rows="3"-->
<!--                maxlength="200"-->
<!--                show-word-limit-->
<!--              ></el-input>-->
<!--            </el-form-item>-->
          </el-form>
        </div>
        <div class="dialog-footer" style="text-align: center;margin-top: 30px;">
          <el-button @click="handleLeftFormReturn">取消</el-button>
          <el-button type="primary" @click="handleLeftFormSave">
            {{ projectFormMode === 0 ? '创建' : '保存' }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Document, Plus } from '@element-plus/icons-vue';

import index_leftForm_mixin from "./mixin/index_left-form_mixin.js";
import index_leftBar_mixin from "./mixin/index_left-bar_mixin.js";

import "../../assets/css/app-start-view_left-right.less"
import {LongArticleProjectMapper} from "../../code/module/long-article/mapper/LongArticleProjectMapper.js";
import Index_projectView from "./components/index_project-view.vue";
import {LongArticleManagerMapper} from "../../code/module/long-article/mapper/LongArticleManagerMapper.js";
// import CACShellMapper from "../../code/mapper/CACShellMapper.js";
import {LongArticleReferenceMapper} from "../../code/module/long-article/mapper/LongArticleReferenceMapper.js";
import LoginService from "../../code/module/platform/service/LoginService.js";

export default {
  name: 'long-article-index',
  components: {
    Index_projectView,
    Document,
    Plus
  },
  mixins: [index_leftBar_mixin, index_leftForm_mixin],
  data() {
    return {
      query: {
        kw: '',
      },

      selectedProject: null,
      // 项目列表
      projects: [
        // { id: '1', name: '测试1', selected: null },
      ],
      
      // 项目表单数据
      projectForm: {
        name: '',
        description: ''
      },


      loading: false,
      updateCode: 0,
      hideLeftBar: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed() {
      this.loading = true;
      await this.loadProjects();
      this.loading = false;
    },
    async onActivated(pars = {}) {
      const self = this;
      console.log('long-article.onActivated', pars);
    },
    async loadProjects() {
      const list = await this.getCtx().longArticleProjectMapper.getProjects(this.query.kw);
      list.forEach((n) => {
        n.selected = null;
      })

      this.projects = list;
      this.selectedProject = null;
    },
  },
  async mounted() {
    const self = this;
    const ctx = this.getCtx();

    // 改用客户端登录服务
    if (ctx.cacShellMapper.isInShell()) {
      console.log('在CacShell中运行');
      ctx.loginService = parent.ctx.loginService;
    }
    else {
      console.log('在WebShell中运行');
      ctx.loginService = new LoginService(ctx);
      await ctx.loginService.init();
    }
    ctx.longArticleProjectMapper = new LongArticleProjectMapper(ctx);
    ctx.longArticleManagerMapper = new LongArticleManagerMapper(ctx);
    ctx.longArticleReferenceMapper = new LongArticleReferenceMapper(ctx);

    // 当激活现有Tab时触发
    window.onActivated = async function (pars) {
      self.onActivated(pars);
    };

    await this.onShowed();
  }
}
</script>

<style scoped lang="less">
.long-article {
  // 所有样式已移至 app-start-view_left-right.less 文件中
}
</style>