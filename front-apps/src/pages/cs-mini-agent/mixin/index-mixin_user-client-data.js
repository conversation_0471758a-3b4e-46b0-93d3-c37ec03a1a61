export default {
    data() {
        return {
        }
    },
    methods: {
        async getClientData_selfAdaptionType() {
            return await this.getCtx().clientDataService.get('selfAdaptionType')
        },
        async loadUserClientDataBySession(sessionId) {
            const obj = await this.getLocalObjectData('chat-session_' + sessionId);
            if (obj) {
                if (obj.agentValues) {
                    this.agentValues = obj.agentValues;
                }
            }
        },
        async saveUserClientDataBySession(sessionId) {
            const obj = {
                agentValues: this.agentValues,
            };
            await this.setLocalObjectData('chat-session_' + sessionId, obj);
        },
        async setLocalObjectData(name, data) {
            await this.getCtx().clientDataService.set(name, JSON.stringify(data));
        },
        async getLocalObjectData(name) {
            var t = await this.getCtx().clientDataService.get(name);
            if (t != null && t.length > 0) {
                return JSON.parse(t);
            }
            return null;
        },
    }
}