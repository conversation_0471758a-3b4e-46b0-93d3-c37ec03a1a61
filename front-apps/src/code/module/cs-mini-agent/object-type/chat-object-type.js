/**
 * 聊天成员
 * @typedef {Object} ChatMember
 * @property {String} id - 成员id
 * @property {String} name - 成员名称
 * @property {String} avatar - 成员头像
 * @property {String} role - 成员角色，取值：user、agent
 */

/**
 * 我的聊天会话列表
 * @typedef {Array<MyChatSession>} MyChatSessionList
 */

/**
 * 聊天消息
 * @typedef {Object} ChatHistoryItem
 * @property {String} id - 消息ID
 * @property {String} content - 消息内容
 * @property {Boolean} isMe - 是否是当前用户发送的消息
 * @property {String} role - 消息角色，取值：user、agent
 * @property {Number} time - 消息发送时间（时间戳）
 * @property {String} senderId - 发送人id（用户名）
 */

/**
 * 聊天历史
 * @typedef {Array<ChatHistoryItem>} ChatHistory
 */

/**
 * 我的聊天会话
 * @typedef {Object} MyChatSession
 * @property {String} id - 会话id
 * @property {String} title - 会话标题
 * @property {String} summary - 会话摘要
 * @property {Array<ChatMember>} otherMembers - 其他成员
 * @property {ChatHistory} chatHistory - 聊天历史
 */

/**
 * 发送消息
 * @typedef {Object} SendMessage
 * @property {String} id - 消息ID
 * @property {String} content - 消息内容
 * @property {String} sessionId - 会话ID
 * @property {String} senderId - 发送人ID（用户名）
 * @property {String} receiverId - 接收人ID（一对一会话）
 * @property {Number} time - 消息发送时间（时间戳）
 * @property {Object} opts - 附加选项
 */

/**
 * 接收消息
 * @typedef {Object} ReceiveMessage
 * @property {String} id - 消息ID
 * @property {String} reply - 回复内容
 * @property {Object} data - 附加数据
 * @property {Array} tool_calls - 工具调用
 * @property {Number} time - 消息接收时间（时间戳）
 * @property {Object} usage - 使用量
 * @property {Number} usage.total - 总使用量
 */

/**
 * 附件
 * @typedef {Object} Attachment
 * @property {String} name - 名称，例：image.png
 * @property {Number} type - 附件类型，例：image
 * @property {String} content - 附件内容，例：data:image/png;base64,ivBOR...
 * @property {String} pushType - 格式，例：b64
 */