export class CACShellMapper {
    constructor() {
        this._debug = false;
    }

    // 判断是否在客户端中打开
    isInShell() {
        try {
            if (top && top.shellMapper && top.shellMapper.isInShell && top.shellMapper.isInShell()) {
                return true;
            }
        } catch (exc) {
        }
        return false;
    }

    async openExternalUrl(url) {
        top.shellMapper.openExternalUrl(url);
    }

    newBaiduMediaRecorder() {
        return top.newBaiduMediaRecorder();
    }
}