<template>
  <div style="width: 800px;height: 100%;overflow-y:auto;margin:auto;" v-loading="loading">
    <div style="margin-bottom: 30px">
      <h3 style="margin-bottom: 5px;">工程违规条目</h3>
      <div>
        1. 没有佩戴安全帽<br/>
        2. 赤裸没有穿衣服<br/>
        3. 工地有渣土车停靠<br/>
      </div>
    </div>
    <table>
      <template v-for="item in testData.items">
        <tr>
          <td>
            <img :src="getUrl(item)" style="height: 300px;" @load="handleImageLoad($event, item)" crossorigin="anonymous" />
          </td>
          <td>&nbsp;</td>
          <td class="test-item">
            <div v-if="item.loading">识别中...</div>
            <div>
              是否违规：{{item.test_has_wg}}
            </div>
            <div>
              违规条目：{{item.test_wg_items}}
            </div>
            <div>
              图中位置：{{item.test_wg_pos}}
            </div>
            <div>
              <span v-if="item.test_wg_result === true" style="color: green;">
                识别判定：正确
              </span>
              <span v-if="item.test_wg_result === false" style="color: red;">
                识别判定：错误
              </span>
            </div>
          </td>
        </tr>
      </template>
    </table>
    <div style="margin-top: 12px;">
      <el-button @click="startTest">开始测试</el-button>
    </div>
  </div>
</template>

<script>
import appConfig from "../../config/app-config.js";
import CBAIOChatModelMapper from "../../code/mapper/CBAIOChatModelMapper.js";
import LoginService from "../../code/module/platform/service/LoginService.js";
import LoginViewModel from "../../code/module/platform/view-model/LoginViewModel.js";
import ChatService from "../../code/module/cs-mini-agent/service/ChatService.js";
import AgentMapper from "../../code/module/cs-mini-agent/mapper/AgentMapper.js";
import ChatMapper from "../../code/module/cs-mini-agent/mapper/ChatMapper.js";

export default {
  // 工程违规识别测试
  name: "test-gcwgsb",
  data() {
    return {
      model: 'albl_qwen-vl-max', // 识别优秀，速度快
      // model: 'br_gpt-4.1', // 识别优秀，速度一般
      // model: 'br_o1', // 识别优秀，速度慢
      // model: 'minimax_abab6.5s-chat', // 识别一般，速度快
      prompt: `图中是一个工地，看下是否有违规现象，具体参考下面的违规条目：
1. 没有佩戴安全帽
2. 赤裸没有穿衣服
3. 工地有渣土车停靠

不要有任何解释，根据要求返回多个字段，用分隔线"|"来分隔：
1. 违规判定，返回是或否
2. 违规条目号，返回数字（如果有多个就用英文逗号分隔）
3. 违规在图中大致位置`,

      // 测试数据
      testData: {
        items: [
          { url: 'wg_0_001.png', has_wg: false },
          { url: 'wg_0_002.png', has_wg: false },
          { url: 'wg_1_001.png', has_wg: true },
          { url: 'wg_1_003.png', has_wg: true },
          { url: 'wg_1_004.png', has_wg: true },
          { url: 'wg_1_006.png', has_wg: true },
        ]
      },

      loading: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed(pars = {}) {
      const self = this;

      self.loading = true;

      let ctx = this.getCtx();
      if (ctx.loginService == null) {
        ctx.loginService = new LoginService(ctx);
        await ctx.loginService.init();
      }
      if (ctx.loginViewModel == null) ctx.loginViewModel = new LoginViewModel(ctx, { self });
      if (ctx.chatService == null) ctx.chatService = new ChatService(ctx);
      if (ctx.agentMapper == null) ctx.agentMapper = new AgentMapper(ctx);
      if (ctx.chatMapper == null) ctx.chatMapper = new ChatMapper(ctx);
      if (ctx.chatModelMapper == null) ctx.chatModelMapper = new CBAIOChatModelMapper(ctx);

      // 登录检查
      await ctx.loginViewModel.checkLogin({
        readyCb() {
          self.loading = false
        },
        cb(pars1) {
          // self.loginUserRealname = pars1.loginUser.realname;
        }
      });

      self.loading = false;
    },
    getUrl(item) {
      return `${appConfig.backRootUrl}data/test-assets/gcwgsb/imgs/${item.url}`;
    },
    async startTest() {
      for (const item of this.testData.items) {
        item.test_has_wg = '';
        item.test_wg_items = '';
        item.test_wg_pos = '';
        item.test_wg_result = null;
        item.loading = false;
      }

      const messages = [
        { role: 'user', content: this.prompt }
      ];
      for (const item of this.testData.items) {
        item.loading = true;
        const result = await this.getCtx().chatModelMapper.chat(this.model, messages, {
          opts: {
            attachments: [
              { type: 'image', content: item.imgBase64 },
            ]
          }
        })
        item.loading = false;
        if (result.success === false) {
          alert(result.msg);
          break;
        }
        else {
          const arr = result.data.reply.split('|')
          item.test_has_wg = arr[0].trim();
          item.test_wg_items = arr[1].trim();
          item.test_wg_pos = arr[2].trim();
          if (item.has_wg && item.test_has_wg === '是') {
            item.test_wg_result = true;
          }
          else if (!item.has_wg && item.test_has_wg === '否') {
            item.test_wg_result = true;
          }
          else {
            item.test_wg_result = false;
          }
        }
        console.log(result)
      }
    },
    async handleImageLoad(e, item) {
      const img = e.target;
      // 创建canvas
      const canvas = document.createElement('canvas');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);
      try {
        const base64String = canvas.toDataURL('image/png');
        item.imgBase64 = base64String;
        // 这里可以将base64String存到item对象里，或者做其他处理
        console.log('base64:', base64String);

      } catch (err) {
        console.error('图片转base64失败', err);
      }
    }
  },
  mounted() {
    this.onShowed();
  }
}
</script>

<style scoped lang="less">
.test-item {
  font-size: 13px;
  line-height: 25px;
}
</style>