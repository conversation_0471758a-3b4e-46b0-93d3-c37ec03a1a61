export default {
    data() {
        return {
            projectForm: {
                name: null,
            },
            projectFormMode: 0,
            projectFormDialogVisible: false,
        }
    },
    methods: {
        handleNewProject() {
            this.projectForm.name = null;
            this.projectFormDialogVisible = true;
        },
        async handleLeftFormSave() {
            if (this.projectForm.name == null) {
                await this.$alert('项目名称不能为空', { type: 'error' });
                return;
            }
            this.loading = true;
            try {
                await this.getCtx().longArticleProjectMapper.saveProject(0, this.projectForm)
                this.projectFormDialogVisible = false;
            } catch (exc) {
                await this.$alert(exc.message, { type: 'error' });
            }
            await this.loadProjects();
            this.loading = false;
        },
        handleLeftFormReturn() {
            this.projectFormDialogVisible = false;
        },
    }
}