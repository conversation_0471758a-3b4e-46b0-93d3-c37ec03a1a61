.app-start-view_left-right {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  
    /* 左栏 */
    .left-bar {
      display: flex;
      flex-direction: column;
      width: 280px;
      height: 100%;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-right: 1px solid rgba(0, 0, 0, 0.1);
      box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
      padding: 20px;
      box-sizing: border-box;
  
      .search-bar {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        
        .search-input-wrapper {
          flex: 1;
        }
        
        .search-btn {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          }
        }
      }
  
      .left-list-area {
        flex-grow: 1;
        height: 0;
        overflow-y: auto;
        
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
          
          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }
        
        .project-list {
          margin-bottom: 20px;
        }
  
        .list-item {
          border-radius: 12px;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          margin-bottom: 12px;
          border: 1px solid rgba(0, 0, 0, 0.08);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
          }
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: rgba(102, 126, 234, 0.3);
            
            &::before {
              transform: scaleX(1);
            }
          }
  
          .label-area {
            display: flex;
            flex-direction: row;
            align-items: center;
            cursor: pointer;
            padding: 16px;
            
            .icon {
              margin-right: 12px;
              
              .project-icon {
                font-size: 24px;
                color: #667eea;
                transition: all 0.3s ease;
              }
            }
  
            .label {
              flex-grow: 1;
              display: flex;
              flex-direction: column;
              
              .project-name {
                font-size: 16px;
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 4px;
                line-height: 1.2;
              }
              
              .project-meta {
                font-size: 12px;
                color: #718096;
                line-height: 1;
              }
            }
  
          }
  
          .ctrl-area {
            display: flex;
            gap: 8px;
            padding: 0 16px 16px;
            justify-content: flex-end;

            img {
              width: 20px;
              height: 20px;
              cursor: pointer;
              vertical-align: middle;
            }
            
            .ctrl-btn {
              border-radius: 6px;
              transition: all 0.3s ease;
              
              &.edit-btn {
                color: #667eea;
                
                &:hover {
                  background-color: rgba(102, 126, 234, 0.1);
                  transform: scale(1.1);
                }
              }
              
              &.delete-btn {
                color: #e53e3e;
                
                &:hover {
                  background-color: rgba(229, 62, 62, 0.1);
                  transform: scale(1.1);
                }
              }
            }
          }
  
        }
  
        .list-item_selected {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-color: #667eea;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
          
          &::before {
            transform: scaleX(1);
          }
          
          .label-area {
            .icon .project-icon {
              color: white;
            }
            
            .label {
              .project-name {
                color: white;
              }
              
              .project-meta {
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }
          
          .ctrl-area {

            img {
              width: 20px;
              height: 20px;
              cursor: pointer;
              vertical-align: middle;
            }

            .ctrl-btn {

              &.edit-btn {
                color: rgba(255, 255, 255, 0.9);
                
                &:hover {
                  background-color: rgba(255, 255, 255, 0.2);
                  color: white;
                }
              }
              
              &.delete-btn {
                color: rgba(255, 255, 255, 0.9);
                
                &:hover {
                  background-color: rgba(255, 255, 255, 0.2);
                  color: white;
                }
              }
            }
          }
        }

        .add-item {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16px;
          border-radius: 12px;
          background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
          border: 2px dashed #cbd5e0;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s ease;
          }
          
          .plus-icon {
            font-size: 20px;
            color: #667eea;
            margin-right: 12px;
            transition: all 0.3s ease;
          }
          
          .add-text {
            color: #4a5568;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
          }
          
          &:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            
            &::before {
              left: 100%;
            }
            
            .plus-icon {
              color: white;
              transform: rotate(90deg);
            }
            
            .add-text {
              color: white;
            }
          }
        }
  
      }
  
      .add-btn-area {
        text-align: center;
        line-height: 50px;
      }
    }
  
    /* 垂直分割线 */
    .separator-bar-v {
      width: 1px;
      height: 100%;
      background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
    }
  
    /* 主内容区域 */
    .main-content {
      flex-grow: 1;
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border-radius: 0 20px 20px 0;
      overflow: hidden;
    }
    
    /* 空状态 */
    .empty-state {
      flex-grow: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border-radius: 0 20px 20px 0;
      
      .empty-content {
        text-align: center;
        max-width: 400px;
        padding: 40px;
        
        .empty-icon {
          font-size: 80px;
          color: #cbd5e0;
          margin-bottom: 24px;
          opacity: 0.8;
        }
        
        .empty-title {
          font-size: 24px;
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 16px;
          line-height: 1.3;
        }
        
        .empty-description {
          font-size: 16px;
          color: #718096;
          line-height: 1.6;
          margin-bottom: 32px;
        }
        
        .create-project-btn {
          padding: 12px 24px;
          font-size: 16px;
          border-radius: 12px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          
          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
          }
        }
      }
    }

    /* 项目弹窗样式 */
    :deep(.project-dialog) {
      .el-dialog {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      }
      
      .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 24px;
        
        .el-dialog__title {
          font-size: 20px;
          font-weight: 600;
        }
        
        .el-dialog__close {
          color: white;
          font-size: 20px;
          
          &:hover {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
      
      .el-dialog__body {
        padding: 32px;
        background: #fafafa;
        
        .dialog-body {
          .el-form-item {
            margin-bottom: 24px;
            
            .el-form-item__label {
              font-weight: 600;
              color: #2d3748;
              font-size: 14px;
              margin-bottom: 8px;
            }
            
            .el-input {
              .el-input__inner {
                border-radius: 8px;
                border: 2px solid #e2e8f0;
                transition: all 0.3s ease;
                
                &:focus {
                  border-color: #667eea;
                  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
              }
            }
            
            .el-textarea {
              .el-textarea__inner {
                border-radius: 8px;
                border: 2px solid #e2e8f0;
                transition: all 0.3s ease;
                resize: none;
                
                &:focus {
                  border-color: #667eea;
                  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
              }
            }
          }
        }
        
        .dialog-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #e2e8f0;
          
          .el-button {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &.el-button--primary {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;
              
              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
              }
            }
            
            &:not(.el-button--primary) {
              border: 2px solid #e2e8f0;
              color: #4a5568;
              
              &:hover {
                border-color: #cbd5e0;
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
    
    /* 输入框样式优化 */
    :deep(.el-input) {
      .el-input__inner {
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
        
        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }
      
      .el-input__prefix {
        color: #718096;
      }
    }
    
    /* 按钮样式优化 */
    :deep(.el-button) {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &.el-button--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
      }
    }
    
    /* 工具提示样式 */
    :deep(.el-tooltip__popper) {
      background: rgba(45, 55, 72, 0.9);
      backdrop-filter: blur(10px);
      border-radius: 6px;
      font-size: 12px;
      padding: 8px 12px;
    }
  }