import CBAIOMapper from "../../../mapper/CBAIOMapper.js"

export default class AgentMapper {
    constructor(ctx) {
        this.ctx = ctx
        this.cbaiomapper = new CBAIOMapper(ctx);
    }
    
    /**
     * 获取智能体前端设置
     * @param {*} id 智能体id
     * @returns 
     */
    async getAgentFrontSettings(id) {
        const r = await this.cbaiomapper.reqByPost('/api/cma/agent/get-agent-front-settings', { id });
        if (!r.success) {
            throw new Error(r.msg);
        }
        if (r.data && r.data.length > 0) {
            return eval(r.data)();
        }
        return null;
    }

    /**
     * 执行智能体插件
     * @param {*} agentId 
     * @param {*} name 
     * @param {*} params 
     * @returns 
     */
    async execAgentPlugin(agentId, name, params) {
        const r = await this.cbaiomapper.reqByPost('/api/cma/agent/exec-agent-plugin', { agentId, name, params });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }
}