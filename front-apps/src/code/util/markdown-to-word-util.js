/**
 * 转换Markdown文本为Word内容（获得内容后保存为.doc格式即可）
 * @param markdownStr
 * @returns {string}
 */
export function convertMarkdownToWord(markdownStr) {
    // 将Markdown转换为HTML
    const htmlContent = markdownToHtml(markdownStr);

    // 创建一个完整的HTML文档，添加msSaveOrOpenBlob支持
    const fullHtml = `
      <!DOCTYPE html>
      <html xmlns:o='urn:schemas-microsoft-com:office:office' 
            xmlns:w='urn:schemas-microsoft-com:office:word'
            xmlns='http://www.w3.org/TR/REC-html40'>
        <head>
          <meta charset="UTF-8">
          <title>Markdown转Word文档</title>
          <xml>
            <w:WordDocument>
              <w:View>Print</w:View>
              <w:Zoom>100</w:Zoom>
              <w:DoNotOptimizeForBrowser/>
            </w:WordDocument>
          </xml>
          <style>
            @page {
              size: 21cm 29.7cm;
              margin: 2cm;
            }
            body { 
              font-family: 'SimSun', Arial, sans-serif; 
              font-size: 12pt;
            }
            h1 { font-size: 18pt; margin-top: 12pt; margin-bottom: 6pt; }
            h2 { font-size: 16pt; margin-top: 10pt; margin-bottom: 5pt; }
            h3 { font-size: 14pt; margin-top: 8pt; margin-bottom: 4pt; }
            p { font-size: 12pt; line-height: 1.5; margin-bottom: 6pt; }
            ul, ol { margin-left: 20pt; }
            li { margin-bottom: 3pt; }
            blockquote { margin-left: 15pt; padding-left: 5pt; border-left: 3pt solid #ccc; }
            code { font-family: Consolas, monospace; background-color: #f0f0f0; padding: 2pt; }
            pre { background-color: #f0f0f0; padding: 5pt; margin: 5pt 0; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;

    return fullHtml;
}

// 简单的Markdown到HTML转换函数
function markdownToHtml(markdown) {
    // 处理标题
    let html = markdown
        .replace(/^###### (.*$)/gim, '<h6>$1</h6>')
        .replace(/^##### (.*$)/gim, '<h5>$1</h5>')
        .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // 处理粗体和斜体
    html = html
        .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/gim, '<em>$1</em>')
        .replace(/\_\_(.*?)\_\_/gim, '<strong>$1</strong>')
        .replace(/\_(.*?)\_/gim, '<em>$1</em>');

    // 处理链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>');

    // 处理图片
    html = html.replace(/!\[([^\]]+)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" />');

    // 处理代码块
    html = html.replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>');

    // 处理行内代码
    html = html.replace(/`([^`]+)`/gim, '<code>$1</code>');

    // 处理无序列表
    html = html.replace(/^\s*[\*\-\+]\s+(.*)/gim, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>\n)+/gim, '<ul>$&</ul>');

    // 处理有序列表
    html = html.replace(/^\s*\d+\.\s+(.*)/gim, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>\n)+/gim, '<ol>$&</ol>');

    // 处理引用
    html = html.replace(/^\>\s+(.*)/gim, '<blockquote>$1</blockquote>');

    // 处理段落
    html = html.replace(/^(?!<[a-z])(.*$)/gim, '<p>$1</p>');

    // 处理换行
    html = html.replace(/\n/gim, '<br>');

    return html;
}