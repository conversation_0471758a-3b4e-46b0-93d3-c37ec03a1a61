import { queryString } from '../../../util/web-util.js';
import CSDESA from '../../../util/CSDESA.js';

export default class LoginViewModel {
    constructor(ctx, cfg) {
        this.ctx = ctx;
        this.cfg = cfg;
        this.debug = cfg.self.debug;
    }

    // 发送顶部消息给用户
    view_sendMessage(msg, type) {
        if (type == null) type = 'success';
        this.cfg.self.$message({
            message: msg,
            type: type
        });
    }

    view_alertError(msg, cb) {
        this.cfg.self.$alert(msg, {
            type: 'error',
            callback: cb
        });
    }

    // // 设置登录用户姓名
    // view_setLoginUserRealname(v) {
    //     this.cfg.self.loginUserRealname = v;
    // }

    // 显示登录窗口
    view_showLoginWin(v) {
        this.cfg.self.$refs.loginWin0.show(v);
    }


    // 加载客户端数据
    async loadClientData() {
        const t = await this.ctx.clientDataService.get('login');
        if (t && t.length > 0) {
            const form = JSON.parse(t);
            return {
                sac: form.sac,
                username: form.un,
                jzmm: form.jzmm,
            }
        }

        return {
            sac: '',
            username: '',
        }
    }

    // 保存客户端数据
    async saveClientData(form) {
        await this.ctx.clientDataService.set('login', JSON.stringify({
            sac: form.sac,
            un: form.username,
            jzmm: form.jzmm,
        }))
    }

    async loadClientDataPassword() {
        const t = await this.ctx.clientDataService.get('login-pwd');
        if (t && t.length > 0) {
            var csdesa = new CSDESA()
            return csdesa.decrypt('5aa76590', t)
        }
    }

    async saveClientDataPassword(pwd) {
        var csdesa = new CSDESA()
        await this.ctx.clientDataService.set('login-pwd', csdesa.encrypt('5aa76590', pwd));
    }

    // 登录检查
    async checkLogin(pars) {
        const self = this;

        const token_str = queryString('token');
        let token_29e65a8f_str = queryString('token_29e65a8f');
        const token_29e65a8f_b64_str = queryString('token_29e65a8f_b64');

        if (token_29e65a8f_b64_str && token_29e65a8f_b64_str.length > 0) {
            token_29e65a8f_str = decodeURIComponent(escape(atob(decodeURIComponent(token_29e65a8f_b64_str))));
        }

        // 检查是否已登录
        const isLogin = await this.ctx.loginService.isLogin();

        // 通过url参数自动登录
        if (token_str && token_str.length > 0) {
            // const k = '!bd3#25a';
            // const token_obj = JSON.parse(new CSDESA().decrypt(k, token_str));
            await this.ctx.loginService.loginByToken(token_str);
        }
        else if (token_29e65a8f_str && token_29e65a8f_str.length > 0) {
            const k = '7dbf8dea';
            const token_29e65a8f_raw = new CSDESA().decrypt(k, token_29e65a8f_str);
            const token_29e65a8f_obj = JSON.parse(token_29e65a8f_raw);
            const now = Date.now();
            const tokenTime = token_29e65a8f_obj.time;
            // 验证token是否过期
            if (now - tokenTime > 5 * 60 * 1000) {
                // token过期
                if (!isLogin) {
                    self.view_alertError(`令牌已过期`, () => {
                        // 重写url地址，删除token_29e65a8f参数
                        const url = new URL(window.location.href);
                        url.searchParams.delete('token_29e65a8f');
                        url.searchParams.delete('token_29e65a8f_b64');
                        window.location.href = url.toString();
                    });
                }
            }
            else {
                if (token_29e65a8f_obj.pwd && token_29e65a8f_obj.pwd.length > 0) {
                    // 用户名密码登录
                    await this.ctx.loginService.login(token_29e65a8f_obj.sac, token_29e65a8f_obj.un, token_29e65a8f_obj.pwd)
                }
                else {
                    // 用户名登录
                    await this.ctx.loginService.loginByUn(token_29e65a8f_obj.sac, token_29e65a8f_obj.un, token_29e65a8f_obj.skey);
                }
            }
        }
        else {
            if (!isLogin) {
                if (pars.readyCb) {
                    pars.readyCb()
                }
                // 没有登录显示登录窗口
                await this.show();
            }
        }

        const loginUser = await self.ctx.loginService.getUser();
        if (loginUser) {
            // this.view_setLoginUserRealname(loginUser.realname);
            if (self.debug) console.log('登录用户：', loginUser)
        }

        if (pars.cb) {
            pars.cb({
                loginUser,
            });
        }
    };

    // 显示登录框
    async show() {
        const self = this;
        // 从客户端加载登录信息
        const form = await this.loadClientData();
        // 加载密码
        if (form.jzmm) {
            form.password = await this.loadClientDataPassword();
        }
        // 加载url参数
        const p_url_sac = queryString('sac');
        if (p_url_sac && p_url_sac.length > 0) {
            form.sac = p_url_sac;
        }

        return await new Promise(function (resolve, reject) {
            self.view_showLoginWin({
                loginForm: form,
                // 当点击登录按钮
                async handleLogin(pars) {
                    let sac = pars.sac;
                    let username = pars.username;
                    let password = pars.password;
                    let jzmm = pars.jzmm;

                    const r = await self.ctx.loginService.login(sac, username, password);
                    pars.doneCallback(r);
                    if (!r.success) {
                    }
                    else {
                        resolve(r.data);
                        // 保存登录信息到客户端
                        self.saveClientData({
                            sac, username, jzmm
                        });
                        // 保存密码
                        if (jzmm) {
                            await self.saveClientDataPassword(password);
                        }
                    }
                }
            });
        });
    }
}