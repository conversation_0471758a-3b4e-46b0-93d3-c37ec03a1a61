<template>
    <div class="list" v-if="list != null && list.length > 0">
        <!-- 遍历附件列表 -->
        <template v-for="(item, index) in list">
            <div class="cell image-cell" v-if="item.type === 'image'">
                <img :src="item.content" :title="item.name" @click="handleOpenImage(item)" />
                <button class="close-btn" @click="removeFile(index)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                        viewBox="0 0 256 256">
                        <path
                            d="M208.49,191.51a12,12,0,0,1-17,17L128,145,64.49,208.49a12,12,0,0,1-17-17L111,128,47.51,64.49a12,12,0,0,1,17-17L128,111l63.51-63.52a12,12,0,0,1,17,17L145,128Z">
                        </path>
                    </svg>
                </button>
            </div>
            <div class="cell file-cell" v-else>
                <i class="el-icon-document" style="font-size: 73px;" :title="item.name"></i>
                <button class="close-btn" @click="removeFile(index)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                        viewBox="0 0 256 256">
                        <path
                            d="M208.49,191.51a12,12,0,0,1-17,17L128,145,64.49,208.49a12,12,0,0,1-17-17L111,128,47.51,64.49a12,12,0,0,1,17-17L128,111l63.51-63.52a12,12,0,0,1,17,17L145,128Z">
                        </path>
                    </svg>
                </button>
            </div>
        </template>
        <div style="clear: both;"></div>
        <div style="height: 10px;">&nbsp;</div>
    </div>
</template>

<script>
export default {
    name: 'ChatInputFiles',
    props: {
    },
    data() {
        return {
            /**
             * 附件列表
             * 样例：[{ name: '', type: '', pushType: '', file: <File>, content: null }]
             * 附件类型 pushType: b64, url
             */
            list: []
        };
    },
    methods: {
        /**
         * @returns {Array<Attachment>}
         */
        getFileList() {
            return this.list;
        },
        addFile(file) {
            const self = this;
            self.convertToBase64(file, (base64) => {
                let type = 'file';
                if (file.type.startsWith('image/')) type = 'image';

                self.list.push({
                    name: file.name, 
                    type: type,
                    pushType: 'b64', 
                    file, 
                    content: base64
                });
            });
        },
        clear() {
            this.list.splice(0);
        },
        convertToBase64(file, cb) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const base64 = e.target.result;
                if (cb) cb(base64);
            };
            reader.readAsDataURL(file);
        },
        removeFile(index) {
            this.list.splice(index, 1);
        },
        /**
         * 在新窗口打开图片
         * @param item 
         */
        handleOpenImage(item) {
            const newWindow = window.open('');
            newWindow.document.write(`
                <html>
                    <head>
                        <title>${item.name}</title>
                        <style>
                            body {
                                margin: 0;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                                background: #f5f5f5;
                            }
                            img {
                                max-width: 100%;
                                max-height: 100vh;
                                object-fit: contain;
                            }
                        </style>
                    </head>
                    <body>
                        <img src="${item.content}" alt="${item.name}" />
                    </body>
                </html>
            `);
            newWindow.document.close();
        },
    }
}
</script>

<style scoped lang="less">
.list {
    .cell {
        float: left;
        position: relative;
        margin: 5px;
        padding: 0;
        width: 50px;
        height: 50px;

        img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            border: 1px solid #ccc;
            cursor: pointer;
        }

        .close-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            background: black;
            color: white;
            border: 1px solid black;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            line-height: 16px;
            text-align: center;
            cursor: pointer;
            padding: 2px 1px 1px 1px;
        }
    }
}
</style>