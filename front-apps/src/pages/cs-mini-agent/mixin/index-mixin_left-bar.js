import { queryString } from '../../../code/util/web-util';


export default {
    data() {
        return {
            // 会话列表，[{ id, title, summary, otherMembers: [{ id, name, role, summary, avatar }] }]
            // sessions: [], 
            currentSession: null, // 当前选中的会话
            hideLeftBar: false,
        }
    },
    methods: {
        async onShowedEndProcLeftBar() {
            let t;

            if ((t = queryString('selectSessionId')) != null && t.length > 0) {
                this.$refs.myChatListBar0.selectSessionById(t);
            }
        }
    },
    mounted() {
        const self = this;

        if (queryString('hideLeftBar') == '1') {
          self.hideLeftBar = true
        }
    }
}