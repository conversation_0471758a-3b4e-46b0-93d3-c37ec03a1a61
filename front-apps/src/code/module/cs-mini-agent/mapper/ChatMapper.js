import { reqPostJson, fetchStream } from "../../../util/http-util.js";
import sysCfg from "../../../../config/app-config.js";
import {newCUIdA} from "../../../util/id-util.js";

export default class ChatMapper {
    constructor(ctx) {
        this.ctx = ctx
    }

    /**
     * 获取我的会话列表
     * @param {String} kw 关键词
     * @returns {MyChatSessionList} 我的会话列表
     */
    async getMyChatSessions(kw, opts) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/get-my-chat-sessions`, {
            token,
            kw: kw,
            ...opts,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    /**
     * 发送消息
     * @param {SendMessage} message 
     * @param {*} opts 
     * @returns {ReceiveMessage} 立刻回复消息
     */
    async sendMessage(message, opts) {
        if (opts == null) opts = {};
        if (opts.reasoningCb == null) opts.reasoningCb = function () { };

        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/send-message`, {
            token,
            message: message,
            ...opts,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }

        // 分离思考内容和回复内容
        if (r.data.reply.startsWith('<think>\n')) {
            const arr = r.data.reply.split('\n</think>\n');
            let reasoning = arr[0];
            let reply = arr[1];

            reasoning = reasoning.substring('<think>\n'.length).trim();

            r.data.reply = reply;
            opts.reasoningCb(reasoning);
        }

        return r.data;
    }

    /**
     * 发送消息（流式）
     * @param {*} message 
     * @param {*} changeCb 
     * @param {*} doneCb 
     * @param {*} opts 
     */
    async sendMessageStream(message, changeCb, doneCb, opts) {
        if (opts == null) opts = {};
        if (opts.reasoningCb == null) opts.reasoningCb = function () { };

        let _totalChatReply = '';
        let chatStreamStateId = newCUIdA();

        const token = await this.ctx.loginService.getToken();
        const fetchResult = await fetchStream(`${sysCfg.backRootUrl}api/cma/chat/send-message-stream`, 'POST', {
            token,
            chatStreamStateId,
            message: message,
            ...opts,
        }, (r) => {
            _totalChatReply += r;

            if (opts.onBuildTotalChatReply) {
                _totalChatReply = opts.onBuildTotalChatReply(_totalChatReply);
            }
            // console.log(_totalChatReply);

            // 执行回复指令
            {
                const regex = /<cs-ai-reply-cmd style="display: none;">([\s\S]*?)<\/cs-ai-reply-cmd>/g;
                const reg = new RegExp(regex);
                const matches = _totalChatReply.match(reg);
                if (matches) {
                    for (const match of matches) {
                        // 解析出指令名删除原文
                        const cmd = match.replace('<cs-ai-reply-cmd style="display: none;">', '').replace('<\/cs-ai-reply-cmd>', '');
                        _totalChatReply = _totalChatReply.replaceAll(match, '');

                        const obj = JSON.parse(cmd);
                        // console.log(`执行指令: ${cmd}`);
                        // 打开网页
                        if (obj.type === 'open-url') {
                            window.open(obj.url);
                        }
                        // 思考状态变更事件
                        else if (obj.type === 'set-think-state') {
                            if (opts.thinkStateChangeCb) {
                                opts.thinkStateChangeCb(obj.value);
                            }
                        }
                        else {
                            console.error(`不支持的指令: ${cmd}`);
                            alert(`不支持的指令: ${obj.type}`);
                        }
                    }
                }
            }

            if (_totalChatReply.indexOf('<think>') !== -1) {
                if (_totalChatReply.indexOf('</think>') !== -1) {
                    const arr = _totalChatReply.split('</think>');
                    if (opts.reasoningCb) {
                        if (arr[0].indexOf('<think>\n') !== -1) {
                            opts.reasoningCb(arr[0].substring('<think>\n'.length).trim());
                        }
                        else {
                            opts.reasoningCb(arr[0].substring('<think>'.length).trim());
                        }
                    }
                    if (changeCb) {
                        changeCb(arr[1]);
                    }
                }
                else {
                    if (opts.reasoningCb) {
                        if (_totalChatReply.indexOf('<think>\n') !== -1) {
                            opts.reasoningCb(_totalChatReply.substring('<think>\n'.length).trim());
                        }
                        else {
                            opts.reasoningCb(_totalChatReply.substring('<think>'.length).trim());
                        }
                    }
                    if (changeCb) {
                        changeCb('');
                    }
                }
            }
            else {
                if (opts.reasoningCb) {
                    opts.reasoningCb('');
                }
                if (changeCb) {
                    changeCb(_totalChatReply);
                }
            }

            // // 输出回复内容
            // aiReplyStreamChunkHelper.build(r, {
            //     debug: opts.debug,
            // });
        }, () => {
            if (doneCb != null) {
                doneCb();
            }
        });

        fetchResult.chatStreamStateId = chatStreamStateId;
        console.log(fetchResult)

        return fetchResult;
    }

    /**
     * 中断流式输出
     * @param chatStreamStateId
     * @returns {Promise<void>}
     */
    async abortSendMessageStream(chatStreamStateId) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/abort-send-message-stream`, {
            token,
            chatStreamStateId
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }

    /**
     * 获取聊天历史记录
     * @param {*} sessionId 
     * @param {*} opts 
     * @returns 
     */
    async getChatHistory(sessionId, opts) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/get-chat-history`, {
            token,
            sessionId: sessionId,
            ...opts,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    /**
     * 推送聊天会话历史记录
     * @param {*} sessionId 
     * @param {*} item
     * @returns {ReceiveMessage} 立刻回复消息
     */
    async pushChatHistoryItem(sessionId, item, opts) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/push-chat-history-item`, {
            token,
            sessionId: sessionId,
            item: item,
            ...opts,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }

    /**
     * 删除聊天会话历史记录
     * @param {*} sessionId 
     * @param {*} id
     * @returns {ReceiveMessage} 立刻回复消息
     */
    async deleteChatHistoryItem(sessionId, id, opts) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/delete-chat-history-item`, {
            token,
            sessionId: sessionId,
            id: id,
            ...opts,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }

    /**
     * 清空聊天会话历史记录
     * @param {*} sessionId 
     * @param {*} opts 
     */
    async clearChatHistory(sessionId, opts) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/chat/clear-chat-history`, {
            token,
            sessionId: sessionId,
            ...opts,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }
}