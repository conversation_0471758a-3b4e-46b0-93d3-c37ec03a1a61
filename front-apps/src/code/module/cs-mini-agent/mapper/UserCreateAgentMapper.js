import { reqPostJson, fetchStream } from "../../../util/http-util.js";
import sysCfg from "../../../../config/app-config.js";

export class UserCreateAgentMapper {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async getMyAgentList() {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/uca/getMyAgentList`, {
            token,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async getMyAgentForm(agentId) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/uca/getMyAgentForm`, {
            token,
            agentId,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async saveMyAgentForm(agentId, form) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/uca/saveMyAgentForm`, {
            token,
            agentId,
            form,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }

    async deleteMyAgent(agentId) {
        const token = await this.ctx.loginService.getToken();
        const r = await reqPostJson(`${sysCfg.backRootUrl}api/cma/uca/deleteMyAgent`, {
            token,
            agentId,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }
}