<!--<script setup>-->
<!--import { ref, onMounted } from 'vue';-->

<!--// 定义props-->
<!--const props = defineProps({-->
<!--    tools: Array,-->
<!--    modelValue: Object,-->
<!--    canSelectFilterable: {-->
<!--        type: Boolean,-->
<!--        default: true-->
<!--    }-->
<!--});-->

<!--// 定义emit-->
<!--const emit = defineEmits(['on-change']);-->

<!--// 响应式变量-->
<!--// const _tools = ref([]);-->
<!--const updateCode = ref(0);-->

<!--// 方法-->
<!--function handleChange() {-->
<!--    updateCode.value++;-->
<!--    emit('on-change');-->
<!--}-->

<!--function handleClick(e) {-->
<!--    e.stopPropagation();-->
<!--}-->

<!--// // 生命周期钩子-->
<!--// onMounted(() => {-->
<!--//     console.log(props.tools);-->
<!--//     console.log(props.modelValue);-->
<!--    -->
<!--//     // 初始化工具列表-->
<!--//     _tools.value = props.tools || [];-->
<!--// });-->
<!--</script>-->

<template>
  <div :update-code="updateCode">
    <template v-for="tool in tools">
      <!-- 下拉选择器 -->
      <template v-if="tool.type === 'select'">
        <el-select v-if="tool.enabled !== false && tool.visible !== false" clearable
                   v-model="modelValue[tool.name]" :placeholder="tool.placeholder"
                   :style="`width: 150px;margin-right:10px;`"
                   @change="handleChange" :filterable="canSelectFilterable" @click="handleClick">
          <el-option v-for="item in tool.options"
                     :key="item.value" :label="item.label" :value="item.value" :title="item.title">
          </el-option>
        </el-select>
      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: 'ChatInputInnerTools',
  props: {
    tools: Array,
    modelValue: Object,
    canSelectFilterable: {
      type: Boolean,
      default: true
    },
  },
  watch: {
    modelValue: function (newVal) {
      this.onShowed();
    },
    tools: function (newVal) {
      this.onShowed();
    }
  },
  data() {
    return {
      updateCode: 0,
    }
  },
  methods: {
    onShowed() {
      if (this.tools) {
        // 检测选择器传入值是否选项中有，如果没有则赋予空值
        for (const tool of this.tools) {
          if (tool.type === 'select') {
            const value = this.modelValue[tool.name];
            if (value != null) {
              let findValue = false;
              for (const option of tool.options) {
                if (option.value === value) {
                  findValue = true;
                  break;
                }
              }

              if (!findValue) {
                this.modelValue[tool.name] = null;
              }
            }
          }
        }
      }
    },
    handleChange() {
      this.updateCode++;
      this.$emit('on-change');
    },
    handleClick(e) {
      e.stopPropagation();
    },
  },
  mounted() {
    const self = this;


  }
}
</script>

<style>

</style>