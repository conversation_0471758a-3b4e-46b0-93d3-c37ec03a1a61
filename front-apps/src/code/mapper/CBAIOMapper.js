import { reqPostJson, fetchStream } from "../util/http-util.js";
import sysCfg from "../../config/app-config.js";

export default class CBAIOMapper {
    constructor(ctx) {
        this.ctx = ctx
    }

    async reqByPost(cmd, params) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        return await reqPostJson(`${sysCfg.backRootUrl}${cmd}`, {
            token: token,
            ...params,
        });
    }

    async reqByPost1(cmd, params) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        return await reqPostJson(`${sysCfg.backRootUrl}${cmd}`, {
            token: token,
            params: params,
        });
    }

    async reqByPostStream(cmd, params, cb, doneCb, errCb) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        fetchStream(`${sysCfg.backRootUrl}${cmd}`, 'POST', {
            token,
            ...params,
        }, (r) => {
            if (cb) cb(r);
        }, () => {
            if (doneCb) doneCb();
        }, (err) => {
            if (errCb) errCb(err);
        });
    }

    /**
     * 下载文件，使用get模式打开下载地址
     * @param cmd
     * @param params
     * @return {Promise<void>}
     */
    async downloadFile(cmd, params) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        // 构建查询参数
        const queryParams = new URLSearchParams({
            token: token,
            ...params,
        });


        // 在新窗口中打开下载链接
        if (this.ctx.cacShellMapper.isInShell()) {
            // 构建完整的下载URL
            const downloadUrl = `${sysCfg.backRealRootUrl}${cmd}?${queryParams.toString()}`;
            this.ctx.cacShellMapper.openExternalUrl(downloadUrl);
        }
        else {
            // 构建完整的下载URL
            const downloadUrl = `${sysCfg.backRootUrl}${cmd}?${queryParams.toString()}`;
            window.open(downloadUrl, '_blank');
        }
    }
}