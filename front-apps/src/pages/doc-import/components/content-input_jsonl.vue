<template>
  <div class="content-input_jsonl" style="flex-grow: 1;">
    <div>
      <el-input
          v-model="value"
          :rows="30"
          type="textarea"
          :placeholder="placeholderText"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragenter="handleDragEnter" @dragleave="handleDragLeave"
      />
    </div>
    <div style="width: 100%;height: 10px;"></div>
    <div style="text-align: center;">
      <el-button type="primary" @click="handleImport">导入</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'content-input_jsonl',
  props: {
  },
  data() {
    return {
      value: null,
      placeholderText: null,
    }
  },
  methods: {
    onShowed(pars = {}) {
      this.placeholderText = pars.placeholderText || ''
    },
    getValue() {
      return this.value;
    },
    handleImport() {
      if (this.value == null || this.value.trim().length === 0) {
        this.$alert('导入内容不能为空', { type: 'error' });
        return;
      }

      this.$emit('on-import', {
        value: this.value,
      })
    },


    handleDragOver(e) {
      e.preventDefault()
      e.stopPropagation()
    },

    handleDragEnter(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = true
    },

    handleDragLeave(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false
    },

    handleDrop(e) {
      const self = this;
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false


      const files = Array.from(e.dataTransfer.files)
      if (files.length > 0) {
        const file = files[0];
        const reader = new FileReader();

        reader.onload = (e) => {
          self.value = e.target.result;
        };
        reader.readAsText(file);
      }
    },
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.content-input_jsonl {

}
</style>