// 变量定义
@primary-color: #1890ff;
@border-color: #ddd;
@text-color: #666;
@transition-curve: cubic-bezier(0.4, 0, 0.2, 1);
@transition-duration: 0.25s;

.cs-mini-agent {
  display: flex;
  height: 90vh;
  background: #f5f5f5;
  position: relative;
  // width: 90%;
  margin: 5vh auto;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 100%;

  // 创建一个包装容器来固定左栏和中栏
  .main-content {
    width: 100%;
    display: flex;
    flex-shrink: 0;
    flex-grow: 1;
    overflow: hidden; // 防止内容溢出
  }

  // 左栏：会话列表
  .session-list {
    width: 250px;
    flex-shrink: 0;
    border-right: 1px solid @border-color;
    background: white;
    display: flex;
    flex-direction: column;

    .list-header {
      padding: 15px;
      font-weight: bold;
      border-bottom: 1px solid #eee;
    }

    .list-content {
      flex: 1;
      overflow-y: auto;
    }

    .session-item {
      padding: 15px;
      cursor: pointer;
      border-bottom: 1px solid #eee;

      &:hover {
        background: #f9f9f9;
      }

      &.active {
        background: #e6f7ff;
      }

      .title {
        font-weight: bold;
        margin-bottom: 5px;
      }

      .summary {
        font-size: 12px;
        color: @text-color;
      }
    }

    .new-button-area {
      padding: 10px 15px;
      border-top: 1px solid #eee;
      
      .add-agent-btn {
        width: 100%;
        padding: 8px 0;
        background: #f0f0f0;
        color: @text-color;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
        
        &:hover {
          background: #e6f7ff;
          color: @primary-color;
        }
        
        i {
          margin-right: 5px;
          font-size: 14px;
        }
        
        span {
          font-size: 14px;
        }
      }
    }

    .bottom-bar {
      height: 50px;
      border-top: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 15px;

      .logout-btn {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f0f0f0;
        }

        img, i {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          font-size: 20px;
        }

        span {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  // 中栏：聊天区
  .chat-area {
    flex-grow: 1;
    flex-shrink: 1; // 允许收缩
    border-right: 1px solid @border-color;
    display: flex;
    flex-direction: column;
    background: white;
    min-width: 0;
    flex-basis: 0;
    overflow: hidden; // 防止内容溢出

    .chat-header {
      padding: 15px;
      font-weight: bold;
      border-bottom: 1px solid #eee;

      .head-right-button {
        margin-left: 20px;
        cursor: pointer;
      }

      .adapt-mode-btn {
        display: flex;
        align-items: center;
        // padding: 8px;
        border-radius: 4px;
        transition: background-color 0.2s;

        img,
        i {
          width: 20px;
          height: 20px;
          font-size: 20px;
        }

        span {
          color: #666;
          font-size: 14px;
        }
      }

      .new-chat-btn {
        float: right;
        height: 20px;
        cursor: pointer;
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 15px 15px;

      .message-item {
        margin-bottom: 15px;

        .message-time {
          font-size: 12px;
          color: #999;
          margin-bottom: 5px;
        }

        :deep(.message-content) {
          display: inline-block;
          padding: 6px 10px;
          background: #f0f0f0;
          border-radius: 4px;
          max-width: 80%;
          word-wrap: break-word;
          word-break: break-all;
          // white-space: pre-wrap; // 会导致行与行之间空了很大
          font-size: 14px;
          line-height: 23px;

          img {
            max-width: 400px;
          }

          pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            // background-color: rgba(0, 0, 0, 0.05);
            padding: 0;
            margin: 0;
            // border-radius: 4px;

            // Markdown样式
            code {
              display: inline-block;
              overflow-x: auto;
              width: 100%;
              background-color: #f4f4f4;
              border: 1px solid #ddd;
              border-radius: 5px;
              padding: 0;
              font-size: 14px;
              font-family: "Söhne Mono", Monaco, "Andale Mono", "Ubuntu Mono", monospace !important;
              line-height: 24px;

              // white-space: pre-wrap;
              // word-wrap: break-word;
            }
          }

          table {
            border-collapse: collapse;

            th {
              border: 1px solid #ddd;
              padding: 6px 10px;
              font-size: 13px;
              white-space: nowrap;
            }

            td {
              border: 1px solid #ddd;
              padding: 6px 10px;
              font-size: 13px;
            }
          }

          // p {
          //   // margin: 8px 0;
          //   &:first-child {
          //     margin-top: 0;
          //   }
          //   &:last-child {
          //     margin-bottom: 0;
          //   }
          // }

          // ul, ol {
          //   margin: 8px 0;
          //   padding-left: 20px;
          // }

          a {
            color: #1890ff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          // h1, h2, h3, h4, h5, h6 {
          //   margin: 12px 0 0 0;
          // }

          p {
            margin: 8px 0;
          }

          // p + p {
          //   margin-top: 0;
          // }
          // SVG代码块预览功能样式
          .svg-code-block {
            position: relative;
            margin: 10px 0;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: hidden;

            .svg-code-header {
              display: flex;
              justify-content: flex-end;
              padding: 5px 10px;
              background: #f8f8f8;
              border-bottom: 1px solid #eee;
              gap: 5px; // 添加按钮之间的间距

              button {
                background: #1890ff;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.3s;

                &:hover {
                  background: #40a9ff;
                }
                
                // 为新窗口打开按钮添加特殊样式
                &.svg-open-new-btn {
                  background: #13c2c2;
                  
                  &:hover {
                    background: #36cfc9;
                  }
                }
              }
            }

            .svg-code-area {
              margin: 0;
              background: #f8f8f8;

              pre {
                margin: 0;
                padding: 10px;
                max-height: 400px;
                overflow: auto;
              }
            }

            .svg-preview-area {
              padding: 20px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: white;
              min-width: 300px;
              min-height: 200px;
              overflow: auto; // 添加滚动功能
              
              svg {
                // max-width: 100%;
                // 移除max-height限制，允许SVG完整显示，通过父容器的滚动来查看
              }
            }
          }
        }

        .message-reasoning-content {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          margin: 8px 0;
          overflow: hidden;
          transition: all @transition-duration @transition-curve;

          .reasoning-header {
            padding: 8px 12px;
            background: #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            text-align: left;

            &:hover {
              background: darken(#e9ecef, 5%);
            }

            .reasoning-title {
              font-weight: 500;
              color: #495057;
            }

            .toggle-icon {
              font-size: 12px;
              color: #6c757d;
              transition: transform @transition-duration @transition-curve;

              &.expanded {
                transform: rotate(180deg);
              }
            }
          }

          .reasoning-content {
            padding: 12px;

            pre {
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
              color: #495057;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }

        &.message-mine {
          text-align: right;

          .message-content {
            background: @primary-color;
            color: white;
          }
        }

        .agent-thinking {
          padding: 10px 0 0 15px;

          img {
            height: 10px;
          }

          span {
            font-size: 13px;
          }
        }

        .agent-saying {
          padding: 10px 0 0 15px;

          img {
            height: 10px;
          }

          span {
            font-size: 13px;
          }
        }
      }
    }

    .chat-input {
      padding: 10px;
      border-top: 1px solid #eee;
      display: flex;
      gap: 10px;
      flex-direction: column;

      textarea {
        width: 100%;
        min-height: 40px;
        line-height: 15px;
        padding: 10px;
        border: 0;
        // border: 1px solid @border-color;
        // border-radius: 4px;
        resize: none;
        outline: none;
        overflow-y: auto;
        box-sizing: border-box;
        font-size: 14px;

        &::placeholder {
          color: #ccc;
        }

        &:focus {
          border-color: @primary-color;
        }
      }

      button {
        padding: 8px 20px;
        background: @primary-color;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        align-self: flex-end;

        &:hover {
          background: lighten(@primary-color, 10%);
        }

        &:disabled {
          background: #ccc;
          cursor: not-allowed;
          opacity: 0.7;
        }
      }

      .stop-button {
        background-color: #dc0000;

        &:hover {
          background: lighten(#dc0000, 10%);
        }
      }

      .botton-left-button {
        float: left;
      }

      .bottom-right-button {
        float: right;
        height: 33px;
        
        img.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  // 右键菜单样式
  .context-menu {
    position: fixed;
    background: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    padding: 5px 0;
    z-index: 3000;

    .menu-item {
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      color: #606266;
      white-space: nowrap;

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 悬浮标签
  .floating-tag {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -40px; // 调整位置到容器外部
    z-index: 1000;
    background: fade(@primary-color, 30%);
    color: white;
    padding: 15px 8px;
    border-radius: 8px 0 0 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    box-shadow: -2px 2px 8px rgba(0, 0, 0, 0.1);
    transition: right @transition-duration @transition-curve;
    will-change: transform, right;
    transform: translateY(-50%) translateZ(0);

    &:hover {
      background: @primary-color;
      padding-right: 12px;
    }

    &.panel-visible {
      background: @primary-color;
    }

    .tag-icon {
      font-size: 20px;
    }

    .tag-text {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.5;
      text-align: center;
    }
  }

  // 背景遮罩
  .panel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1001;
  }

  // 遮罩动画
  .fade-enter-active {
    transition: opacity 0.2s ease-out;
  }

  .fade-leave-active {
    transition: opacity 0.15s ease-in;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  // 右栏：交互面板
  .interaction-panel {
    position: absolute;
    top: 0;
    height: 100%;
    background: white;
    z-index: 1002;
    // box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    transition: right @transition-duration @transition-curve;
    will-change: transform;
    transform: translateZ(0);

    &.panel-visible {
      right: 0;
    }

    .panel-header {
      padding: 15px;
      font-weight: bold;
      border-bottom: 1px solid #eee;
    }

    .panel-content {
      padding: 15px;
      height: calc(100% - 51px);
      overflow-y: auto;
    }
  }

  // 交互面板链接样式
  .open-interaction-panel-link {
    margin: 4px 0;

    a {
      color: #bebebe;
      text-decoration: none;
      font-size: 13px;

      &:hover {
        color: @primary-color;
        text-decoration: underline;
      }
    }
  }
}