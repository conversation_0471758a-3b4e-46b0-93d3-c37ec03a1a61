<template>
    <div v-if="settings.visible" :update-code="updateCode" style="">
        <template v-if="settings.type === 'table'">
            <!-- 根据columns生成表格定义 -->
            <el-table :data="settings.rows" border style="width: 100%" :height="mainAreaHeight"
                @selection-change="handleSelectionChange">
                <el-table-column v-if="settings.canMultiSelect" type="selection" width="39">
                </el-table-column>
                <template v-for="(column, index) in settings.columns">
                    <el-table-column :prop="column.name" :label="column.label" :width="column.width"
                        :show-overflow-tooltip="column.showOverflowTooltip">
                    </el-table-column>
                </template>
            </el-table>
        </template>
        <div v-if="settings.ctrlLabel" style="line-height: 80px;text-align: center;">
            <span style="font-size: 20px;">{{ settings.ctrlLabel }}</span>
        </div>
        <div v-if="settings.buttons" style="line-height: 80px;text-align: center;">
            <template v-for="(button, index) in settings.buttons">
                <span v-if="index > 0">&emsp;</span>
                <el-button :type="getButtonType(button)" size="small" @click="handleButtonClick(button)">{{
                    button.label }}</el-button>
            </template>
        </div>
    </div>
</template>

<script>
/* 注意：上传后表格空白问题（不要用mac电脑来打包） */
export default {
    name: 'InteractionPanel',
    data() {
        return {
            test: {
                items: [
                    { name: '111' }
                ]
            },
            updateCode: 0,
            mainAreaHeight: 0,
            settings: {
                visible: false,
                type: null,
                canMultiSelect: false,
                columns: [],
                rows: [],
                ctrlLabel: null,
                buttons: null,
            },
            multipleSelection: [],
        }
    },
    methods: {
        reset() {
            for (const prop in this.settings) {
                if (prop === 'columns' || prop === 'rows') {
                    this.settings[prop].splice(0, this.settings[prop].length);
                }
                else {
                    this.settings[prop] = null;
                }
            }
        },
        setSettings(settings) {
            this.reset();
            for (let prop in settings) {
                if (prop === 'columns' || prop === 'rows') {
                    this.settings[prop].splice(0, this.settings[prop].length);
                }
                else {
                    this.settings[prop] = settings[prop];
                }
            }
            settings.columns.forEach(column => {
                this.settings.columns.push(column);
            });
            settings.rows.forEach(row => {
                this.settings.rows.push(row);
            });
        },
        getButtonType(button) {
            if (button.name === 'back') {
                return null;
            }
            else {
                return 'primary';
            }
        },
        resize(cfg) {
            let extraHeight = 75;
            if (this.settings.ctrlLabel) extraHeight += 80;
            if (this.settings.buttons) extraHeight += 80;
            this.mainAreaHeight = cfg.height - extraHeight;
        },
        handleButtonClick(button) {
            const self = this;
            if (button.name === 'submit') {
                this.$emit('on-submit', {
                    invokeComp: self,
                    settings: self.settings,
                    multipleSelection: self.multipleSelection,
                });
            }
            else if (button.name === 'back') {
                this.$emit('on-back');
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        }
    }
}
</script>

<style scoped lang="less">
// .el-button {
//     margin-left: 30px;
// }</style>