<template>
  <div class="chat-message-list">
    <div v-for="msg in messages" :class="{ 'message-container': true }">
      <div :class="{ 'message': true, 'left-message': isLeftMessage(msg), 'right-message': isRightMessage(msg) }">
        <template v-if="isLeftMessage(msg)">
          <div v-html="renderMarkdown(msg.content)"></div>
        </template>
        <template v-else-if="isRightMessage(msg)">
          {{ msg.content }}
        </template>
      </div>
      <div style="clear: both;"></div>
    </div>
    <div class="thinking" v-if="_isThinking">
      <img :src="loadingGif" style="height: 10px;" /><span>（思考中）</span>
    </div>
  </div>
</template>

<script>
import {marked} from "marked";
import loadingGif from "../assets/img/loading.gif";

export default {
  name: "chat-message-list",
  props: {
    messages: [],
  },
  data() {
    return {
      _isThinking: false,
      loadingGif,
    }
  },
  methods: {
    isThinking(val) {
      if (val != null) {
        this._isThinking = val;
      }
      return this._isThinking;
    },
    isLeftMessage(msg) {
      if (msg.role === 'assistant') return true;
      return false;
    },
    isRightMessage(msg) {
      if (msg.role === 'user') return true;
      return false;
    },
    addTextToLastLeftMessage(text) {
      if (this.messages.length > 0) {
        let lastMsg = this.messages[this.messages.length - 1];
        if (this.isLeftMessage(lastMsg)) {
          lastMsg.content += text;
        }
        else {
          this.messages.push({
            role: 'assistant',
            content: text,
          })
        }
      }
      else {
        this.messages.push({
          role: 'assistant',
          content: text,
        })
      }
    },
    setTextToLastLeftMessage(text) {
      if (this.messages.length > 0) {
        let lastMsg = this.messages[this.messages.length - 1];
        if (this.isLeftMessage(lastMsg)) {
          lastMsg.content = text;
        }
        else {
          this.messages.push({
            role: 'assistant',
            content: text,
          })
        }
      }
      else {
        this.messages.push({
          role: 'assistant',
          content: text,
        })
      }
    },
    renderMarkdown(content) {
      try {
        // 检查内容是否需要markdown解析
        const hasMarkdown = /[*#\[\]`>-]/.test(content);
        const hasMarkdown_1 = /\d+[.]+\s+/.test(content); // 检查是否有有序列表
        const hasMarkdown_2 = content.indexOf('\n') !== -1;
        if (!hasMarkdown && !hasMarkdown_1 && !hasMarkdown_2) {
          return content;
        }

        return marked.parse(content);
      } catch (error) {
        console.error('Markdown rendering error:', error);
        return content;
      } finally {
        // this.$nextTick(() => {
        //   if (this.$refs.messageContent0) {
        //     this.$refs.messageContent0.forEach((n) => {
        //       if (n.hasRepairDone == null) {
        //         n.hasRepairDone = true;
        //         this.buildChatLastOtherMessage(n);
        //       }
        //     })
        //   }
        // })
      }
    },
  }
}
</script>

<style scoped lang="less">
.chat-message-list {
  height: 100%;

  .message-container {
    margin-bottom: 10px;

    .empty-block {
      width: 50px;
    }

    .message {
      padding: 10px;
      max-width: 80%;
      font-size: 14px;

      :deep(p) {
        margin-top: 5px;
        margin-bottom: 5px;
      }

      :deep(img) {
        max-width: 80%;
      }
    }

    .left-message {
      float: left;
      margin-left: 12px;
      background-color: #f0f0f0;
      border-radius: 10px;
    }

    .right-message {
      float: right;
      margin-right: 12px;
      background-color: #e6f7ff;
      border-radius: 10px;
    }
  }

  .thinking {
    padding-left: 10px;
  }
}
</style>