import { createRouter, createWebHashHistory } from 'vue-router'

// 
const routes = [
  {
    path: '/doc-import',
    components: {
      main: () => import('./pages/doc-import/index.vue')
    }
  },
  {
    path: '/long-article',
    components: {
      main: () => import('./pages/long-article/index.vue')
    }
  },
  {
    path: '/cs-mini-agent',
    components: {
      main: () => import('./pages/cs-mini-agent/index.vue')
    }
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router