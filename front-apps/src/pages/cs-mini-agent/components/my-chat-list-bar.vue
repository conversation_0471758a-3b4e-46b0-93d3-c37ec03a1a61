<template>
  <div class="my-chat-list-bar">
    <!-- 搜索框 -->
    <div class="search-box">
      <div style="width:10px;">&nbsp;</div>
      <input type="text" v-model="searchKeyword" @input="onSearch" placeholder="搜索会话..." class="search-input">
      <div style="width:10px;">&nbsp;</div>
    </div>

    <!-- 会话列表 -->
    <div class="session-list">
      <div v-for="session in myChatSessionsFilted" :key="session.id"
           :class="['session-item', { active: currentSessionId === session.id }]" @click="selectSession(session)">
        <div class="title">{{ getSessionTitle(session) }}</div>
        <div class="summary">{{ getSessionSummary(session) }}</div>
      </div>
      <div v-for="session in myAgentListFilted" :key="session.id"
           :class="['session-item', { active: currentSessionId === session.id }]" @click="selectSession(session)">
        <div class="title">{{ getSessionTitle(session) }}</div>
        <div class="summary">{{ getSessionSummary(session) }}</div>
      </div>
      <div v-if="myChatSessions.length === 0 && myAgentList.length" class="empty-tip">
        暂无会话记录
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 会话列表组件
 */
export default {
  name: 'MyChatListBar',
  data() {
    return {
      myChatSessions: [], // 会话列表
      myChatSessionsFilted: [], // 会话列表(过滤后)

      myAgentList: [],
      myAgentListFilted: [],

      searchKeyword: '', // 搜索关键词
      currentSessionId: null, // 当前选中的会话ID
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    /**
     * 组件显示时触发
     */
    async onShowed() {
      await this.loadData();
      await this.loadUcaData()
    },

    isMyAgentSelected() {
      if (this.currentSessionId) {
        for (const myAgent of this.myAgentList) {
          if (myAgent.id === this.currentSessionId) {
            return true;
          }
        }
      }
      return false;
    },

    async loadDataAll() {
      await this.loadData();
      await this.loadUcaData()
    },

    /**
     * 加载数据
     */
    async loadData() {
      try {
        const list = await this.getCtx().chatMapper.getMyChatSessions(this.searchKeyword);
        list.forEach((session) => {
          if (session.title == null) {
            if (session.otherMembers.length === 1) {
              session.title = session.otherMembers[0].name;
            }
          }
          if (session.summary == null) {
            if (session.otherMembers.length === 1) {
              session.summary = session.otherMembers[0].summary;
            }
          }
        });

        this.myChatSessions = list;
        this.myChatSessionsFilted = list;
      } catch (error) {
        console.error('加载会话列表失败:', error);
        // 可以添加错误提示
      }
    },

    async loadUcaData() {
      this.myAgentList = await this.getCtx().userCreateAgentMapper.getMyAgentList()
      this.myAgentListFilted = this.myAgentList;
    },

    /**
     * 搜索会话
     */
    async onSearch() {
      const searchKeyword = this.searchKeyword;
      if (searchKeyword && searchKeyword.length > 0) {
        this.myChatSessionsFilted =
            this.myChatSessions.filter((session) =>
                session.title.indexOf(searchKeyword) !== -1 || session.summary.indexOf(searchKeyword) !== -1);
        this.myAgentListFilted = this.myAgentList.filter((n) =>
            n.title.indexOf(searchKeyword) !== -1 || n.summary.indexOf(searchKeyword) !== -1)
      }
      else {
        this.myChatSessionsFilted = this.myChatSessions;
        this.myAgentListFilted = this.myAgentList
      }
    },

    /**
     * 选择会话
     */
    selectSession(session) {
      this.currentSessionId = session.id;
      this.$emit('on-select-session', session);
    },

    /**
     * 选择会话
     */
    selectSessionById(sessionId) {
      this.currentSessionId = sessionId;
      const findSession = this.myChatSessions.find((n) => n.id === sessionId)
      this.$emit('on-select-session', findSession);
    },

    getSelectedSessionId() {
      return this.currentSessionId;
    },

    getSessionTitle(session) {
      if (session.title) return session.title;
      return '未命名会话';
    },

    getSessionSummary(session) {
      if (session.summary) return session.summary;
      return '';
    },
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>
@import '../assets/css/my-chat-list-bar.less';
</style>