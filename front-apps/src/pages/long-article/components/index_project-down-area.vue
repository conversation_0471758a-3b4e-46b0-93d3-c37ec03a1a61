<template>
  <div class="project-down-area">
    <div class="left-area">
      <!-- 大纲编辑区域 -->
      <div class="outline-section">
        <div style="height: 12px;">&nbsp;</div>
        <div class="section-header">
          <h3>项目大纲</h3>
        </div>
        <el-input v-model="outline" type="textarea" placeholder="项目大纲，可在此修改..." class="outline-editor" @change="handleOutlineChange" />
        <div style="height: 12px;">&nbsp;</div>
      </div>
    </div>
    <div class="right-area">
      <!-- 生成任务状态展示 -->
      <div class="generate-status-section">
        <div style="height: 12px;">&nbsp;</div>
        <div class="section-header" style="position: relative;">
          <h3>
            <span>生成预览</span>
            <span>（</span>
            <span v-html="getStatusType(generateState.key)"></span>
            <span>）</span>
          </h3>
          <div style="position: relative;">
            <img v-if="isTaskDoing" src="../../../assets/img/loading.gif" style="height: 50px;position: absolute;top: -15px;" />
            <el-link v-if="isTaskDoing" type="danger" @click="handleStop" class="action-link" style="width: 60px;height: 50px;position: absolute;top: -15px;left: 60px;">停止</el-link>
          </div>
          <div style="flex-grow: 1;">&nbsp;</div>
          <div class="header-actions">
            <el-link type="primary" @click="viewLongArticle" class="action-link">查看长文</el-link>
            <el-link type="primary" @click="downloadLongArticle" class="action-link">下载长文</el-link>
<!--            <el-link type="primary" @click="viewHistoryArticle" class="action-link">历史长文</el-link>-->
          </div>
          <div style="width: 30px;">&nbsp;</div>
        </div>

        <!-- 生成内容预览 -->
        <el-input ref="contentPreviewInput0" v-model="generateState.contentPreview" type="textarea" class="content-preview" readonly
                  style="flex-grow: 1;" />
<!--        <div class="content-preview">-->
<!--&lt;!&ndash;          <div class="preview-header">&ndash;&gt;-->
<!--&lt;!&ndash;            <span>内容预览</span>&ndash;&gt;-->
<!--&lt;!&ndash;            <el-button size="small" @click="clearPreview">清空</el-button>&ndash;&gt;-->
<!--&lt;!&ndash;          </div>&ndash;&gt;-->
<!--          <div class="preview-content" ref="previewContent">-->
<!--            <div v-if="generateState.contentPreview" class="preview-text">-->
<!--              {{ generateState.contentPreview }}-->
<!--            </div>-->
<!--            <div v-else class="preview-placeholder">-->
<!--              暂无生成内容-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
        <div style="height: 12px;">&nbsp;</div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 下半部分区域（项目大纲、生成预览）
 */
export default {
  name: 'index_project-down-area',
  data() {
    return {
      isTaskDoing: false,
      // 大纲内容
      outline: null,
      // 生成状态
      generateState: {
        // 状态键：PENDING 待生成、GENERATING-OUTLINE 生成大纲中、GENERATING-ARTICLE 生成长文中、FAILED 已失败
        // GENERATE-OUTLINE-END 大纲生成完毕、ALL-DONE 生成完毕
        // CANCEL-HOOK 取消监控
        key: 'PENDING',
        // 生成内容预览展示
        contentPreview: '',
      }
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    setIsTaskDoing(v)  {
      this.isTaskDoing = v;
    },
    getOutline() {
      return this.outline;
    },
    setOutline(str) {
      this.outline = str;
    },
    setState(key) {
      this.generateState.key = key;
    },
    // 清空预览内容
    clearContentPreview() {
      this.generateState.contentPreview = '';
    },

    pushContentPreviewChunk(chunk) {
      this.generateState.contentPreview += chunk;
      // 使用 nextTick 确保在 DOM 更新后执行滚动
      this.$nextTick(() => {
        const textarea = this.$refs.contentPreviewInput0.$refs.textarea;
        if (textarea) {
          textarea.scrollTop = textarea.scrollHeight;
        }
      });

    },

    // 获取状态类型
    getStatusType(key) {
      const statusMap = {
        'PENDING': '<span style="color:gray;">待生成</span>',
        'GENERATING': '<span style="color:blue;">生成中</span>',
        'GENERATING-OUTLINE': '<span style="color:blue;">生成大纲中</span>',
        'GENERATING-ARTICLE': '<span style="color:blue;">生成长文中</span>',
        'GENERATE-OUTLINE-END': '<span style="color:green;">生成大纲完毕</span>',
        'GENERATE-ARTICLE-END': '<span style="color:green;">生成长文完毕</span>',
        'ALL-DONE': '<span style="color:green;">生成完毕</span>',
        'FAILED': '<span style="color:red;">已失败</span>',
        'CANCEL-HOOK': '<span style="color:darkred">已取消监控</span>',
      };
      return `&nbsp;${statusMap[key || 'PENDING']}&nbsp;` || `未知 - ${key}`;
    },

    // 更新生成状态
    updateGenerateState(name, key, contentPreview = null) {
      this.generateState.name = name;
      this.generateState.key = key;
      if (contentPreview !== null) {
        this.generateState.contentPreview = contentPreview;
        // 自动滚动到底部
        this.$nextTick(() => {
          const previewEl = this.$refs.previewContent;
          if (previewEl) {
            previewEl.scrollTop = previewEl.scrollHeight;
          }
        });
      }
    },

    handleOutlineChange(e) {
      this.$emit('on-outline-change', e);
    },

    // 查看长文
    viewLongArticle() {
      this.$emit('view-long-article');
    },

    // 下载长文
    downloadLongArticle() {
      this.$emit('download-long-article');
    },

    // 查看历史长文
    viewHistoryArticle() {
      this.$emit('view-history-article');
    },

    handleStop() {
      this.$emit('stop-task')
    },
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.project-down-area {
  display: flex;
  flex-direction: row;
  height: 100%;
  gap: 16px;
  // padding: 0 16px;
  // background: #f8fafc;
  // border-radius: 12px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .left-area {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .outline-section {
      background: #fff;
      border-radius: 8px;
      padding: 0 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      flex: 1;
      display: flex;
      flex-direction: column;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h3 {
          margin: 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }
      }

      .outline-editor {
        flex: 1;

        :deep(.el-textarea__inner) {
          border-radius: 6px;
          //border: 1px solid #dcdfe6;
          font-family: 'Consolas', 'Monaco', monospace;
          line-height: 1.6;
          height: 100% !important;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }
  }

  .right-area {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .generate-status-section {
      background: #fff;
      border-radius: 8px;
      padding: 0 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      flex: 1;
      display: flex;
      flex-direction: column;

      .section-header {
        display: flex;
        //justify-content: space-between;
        //align-items: center;
        margin-bottom: 12px;

        h3 {
          margin: 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .header-actions {
          display: flex;
          gap: 8px;
          align-items: center;

          .action-link {
            padding: 4px 8px;
            font-size: 12px;
            color: #409eff;
            text-decoration: none;
            border: none;
            background: none;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              color: #66b1ff;
              background-color: rgba(64, 158, 255, 0.1);
              border-radius: 4px;
            }

            &:active {
              color: #3a8ee6;
            }
          }
        }
      }

      .content-preview {
        flex: 1;
        display: flex;
        flex-direction: column;

        :deep(.el-textarea__inner) {
          border-radius: 6px;
          //border: 1px solid #dcdfe6;
          font-family: 'Consolas', 'Monaco', monospace;
          line-height: 1.6;
          height: 100% !important;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          padding-bottom: 8px;
          border-bottom: 1px solid #ebeef5;

          span {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }
        }

        .preview-content {
          flex: 1;
          border: 1px solid #dcdfe6;
          border-radius: 6px;
          padding: 0 12px;
          background: #fafafa;
          overflow-y: auto;

          .preview-text {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            word-break: break-word;
          }

          .preview-placeholder {
            color: #909399;
            font-style: italic;
            text-align: center;
            padding: 40px 0;
          }

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }
}
</style>