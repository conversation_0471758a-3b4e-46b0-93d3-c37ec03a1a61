import CBAIOMapper from "../../../mapper/CBAIOMapper.js"
/**
 * 知识库数据源
 */
export class KbDataSourceMapper {
    constructor(ctx) {
        this.ctx = ctx
        this.cbaiomapper = new CBAIOMapper(ctx);
    }

    /**
     * 推送知识库碎片
     * @returns {Promise<*>}
     */
    async pushKf(kbId, title, content) {
        const r = await this.cbaiomapper.reqByPost('/api/cmk/kb/push-kf', {
            kbId,
            title,
            content,
        });
        if (!r.success) {
            throw new Error(r.msg);
        }
    }

    /**
     * 获取我能访问的知识库列表
     * @returns {Promise<*|null>}
     */
    async getMyAccessKBList() {
        const r = await this.cbaiomapper.reqByPost('/api/cmk/kb/get-my-access-kb-list', { });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    /**
     * 获取我管理的知识库列表
     * @returns {Promise<*|null>}
     */
    async getMyManageKBList() {
        const r = await this.cbaiomapper.reqByPost('/api/cmk/kb/get-my-manage-kb-list', { });
        if (!r.success) {
            throw new Error(r.msg);
        }
        return r.data;
    }
}